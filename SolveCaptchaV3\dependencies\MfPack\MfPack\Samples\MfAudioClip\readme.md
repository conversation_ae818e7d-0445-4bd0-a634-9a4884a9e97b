# AudioClip
Version: X 3.1.7

NOTES: 
 - This release is updated for compiler version 17 up to 34.
 - SDK version: 10.0.26100.0 (<PERSON> 11)
 - Requires Windows 7 or later.
 - Minimum supported MfPack version: 3.1.5

Description:

  Demonstrates using the IMFSourceReader API to get 
  uncompressed media data from a media file.

  This sample application reads audio data from a media file and
  writes the uncompressed audio to a WAVE file.
  The input file must be a media format supported by Media Foundation,
  and must have  an audio stream. The audio stream can be an encoded
  format, such as Windows Media Audio.
 
  The original Microsoft sample, is a console app. 
  This sample, however, is not.


Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 21-11-2019
Final release date: 30-05-2024


Copyright © FactoryX. All rights reserved.