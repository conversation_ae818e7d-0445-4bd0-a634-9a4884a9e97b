@echo off
echo ========================================
echo  TESTE DE SINTAXE DO LOGGER
echo ========================================
echo.

echo Verificando sintaxe do uLogger.pas...
cd WindowlessBrowser

echo Tentando compilar apenas o uLogger.pas...
dcc32 -B -U"..\dependencies\WebView4Delphi\source" -U"..\dependencies\MfPack\MfPack\src" -$D+ -$L+ -$Y+ --no-config uLogger.pas

if errorlevel 1 (
    echo ❌ ERRO: Problemas de sintaxe no uLogger.pas
    echo Verifique os erros acima
) else (
    echo ✅ uLogger.pas compilou sem erros de sintaxe
)

echo.
echo ========================================
echo  TESTE FINALIZADO
echo ========================================
pause
