program TestRunner;

{$APPTYPE CONSOLE}

uses
  System.SysUtils,
  System.Classes,
  uTests,
  uConfig,
  uLogger;

var
  TestSuite: TTestSuite;
  
begin
  try
    WriteLn('========================================');
    WriteLn(' TESTE UNITÁRIO SolveCaptchaV3');
    WriteLn('========================================');
    WriteLn;
    
    TestSuite := TTestSuite.Create;
    try
      WriteLn('Executando testes unitários...');
      WriteLn;
      
      TestSuite.RunAllTests;
      TestSuite.GenerateReport;
      
      WriteLn;
      WriteLn('Relatório salvo em: test_report.txt');
      TestSuite.SaveReportToFile('test_report.txt');
      
    finally
      TestSuite.Free;
    end;
    
    WriteLn;
    WriteLn('Pressione ENTER para continuar...');
    ReadLn;
    
  except
    on E: Exception do
    begin
      WriteLn('ERRO: ' + E.Message);
      WriteLn;
      WriteLn('Pressione ENTER para continuar...');
      ReadLn;
    end;
  end;
end.
