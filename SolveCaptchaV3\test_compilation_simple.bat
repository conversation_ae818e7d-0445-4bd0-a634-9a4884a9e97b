@echo off
echo ========================================
echo  TESTE DE COMPILAÇÃO SIMPLIFICADO
echo ========================================
echo.

echo Verificando se o Delphi está disponível...
where dcc32 >nul 2>&1
if errorlevel 1 (
    echo ❌ dcc32 não encontrado no PATH
    echo Verifique se o Delphi está instalado e no PATH
    pause
    exit /b 1
) else (
    echo ✅ dcc32 encontrado
)

echo.
echo Verificando dependências...

if not exist "dependencies\WebView4Delphi\source" (
    echo ❌ WebView4Delphi não encontrado
    pause
    exit /b 1
) else (
    echo ✅ WebView4Delphi encontrado
)

if not exist "dependencies\MfPack\MfPack\src" (
    echo ❌ MfPack não encontrado
    pause
    exit /b 1
) else (
    echo ✅ MfPack encontrado
)

echo.
echo Tentando compilar projeto...
cd WindowlessBrowser

echo Compilando com paths das dependências...
dcc32 -B -U"..\dependencies\WebView4Delphi\source" -U"..\dependencies\MfPack\MfPack\src" WindowlessBrowser.dpr

if errorlevel 1 (
    echo.
    echo ❌ ERRO: Falha na compilação
    echo.
    echo Possíveis soluções:
    echo 1. Abrir o projeto no Delphi IDE
    echo 2. Verificar se todas as dependências estão corretas
    echo 3. Compilar manualmente no IDE
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ✅ COMPILAÇÃO BEM-SUCEDIDA!
    echo.
    if exist "WindowlessBrowser.exe" (
        echo ✅ Executável gerado: WindowlessBrowser.exe
        for %%A in (WindowlessBrowser.exe) do (
            echo 📊 Tamanho: %%~zA bytes
        )
    ) else (
        echo ⚠️ Executável não encontrado no diretório atual
    )
)

echo.
echo ========================================
echo  TESTE FINALIZADO
echo ========================================
pause
