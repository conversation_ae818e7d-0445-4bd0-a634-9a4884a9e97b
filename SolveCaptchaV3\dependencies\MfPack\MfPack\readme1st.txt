MfPack X 3.1.7

NOTES: 
 - This release is updated for compiler version 17 up to 35.
 - SDK version 10.0.22621.0
 - Requires Windows 10 or later.

First release date: 04/06/2012
Final release date: 19/06/2024

Copyright © FactoryX. All rights reserved.

**Install notes**

1 - You don't have to Install the package, just compiling will do.
2 - Make sure you include the path ..\MfPack\src in your project search path.
3 - Recommended project settings:
    - Project output: .\$(Platform)\$(Config)
    - Unit output: .\$(Platform)\$(Config)



