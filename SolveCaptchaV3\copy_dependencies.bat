@echo off
echo ========================================
echo  COPIANDO DEPENDENCIAS - SolveCaptchaV3
echo ========================================
echo.

echo Copiando WebView2Loader.dll...
copy "dependencies\WebView4Delphi\bin32\WebView2Loader.dll" "WindowlessBrowser\" >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Falha ao copiar WebView2Loader.dll
) else (
    echo ✅ WebView2Loader.dll copiado com sucesso
)

echo.
echo Verificando se o arquivo foi copiado...
if exist "WindowlessBrowser\WebView2Loader.dll" (
    echo ✅ WebView2Loader.dll encontrado no diretório do projeto
) else (
    echo ❌ WebView2Loader.dll NÃO encontrado no diretório do projeto
)

echo.
echo Listando arquivos no diretório WindowlessBrowser:
dir "WindowlessBrowser\*.dll" /b 2>nul
if errorlevel 1 (
    echo Nenhum arquivo .dll encontrado
)

echo.
echo ========================================
echo  COPIA FINALIZADA
echo ========================================
pause
