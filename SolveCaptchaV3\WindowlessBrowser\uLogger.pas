unit uLogger;

interface

uses
  System.SysUtils, System.Classes, System.SyncObjs, System.IOUtils,
  Vcl.Forms, uConfig;

type
  TLogLevel = (llDebug, llInfo, llWarning, llError);

  TLogger = class
  private
    FLogFile: string;
    FLogLevel: TLogLevel;
    FEnabled: Boolean;
    FLock: TCriticalSection;
    FLogStream: TFileStream;
    FMaxFileSize: Int64;

    function LogLevelToString(Level: TLogLevel): string;
    function StringToLogLevel(const LevelStr: string): TLogLevel;
    procedure WriteToFile(const Message: string);
    procedure CheckFileSize;
    procedure RotateLogFile;

  public
    constructor Create;
    destructor Destroy; override;

    procedure Initialize(const LogFile: string; const LogLevel: string; Enabled: Boolean);
    procedure Log(Level: TLogLevel; const Message: string); overload;
    procedure Log(Level: TLogLevel; const Format: string; const Args: array of const); overload;

    // Métodos de conveniência
    procedure Debug(const Message: string); overload;
    procedure Debug(const Format: string; const Args: array of const); overload;
    procedure Info(const Message: string); overload;
    procedure Info(const Format: string; const Args: array of const); overload;
    procedure Warning(const Message: string); overload;
    procedure Warning(const Format: string; const Args: array of const); overload;
    procedure Error(const Message: string); overload;
    procedure Error(const Format: string; const Args: array of const); overload;

    // Métodos específicos para CAPTCHA
    procedure LogCaptchaStart(const URL: string);
    procedure LogCaptchaSuccess(const Action: string; Confidence: Double; TimeTaken: Integer);
    procedure LogCaptchaFailure(const Reason: string; TimeTaken: Integer);
    procedure LogAPIRequest(const RequestData: string);
    procedure LogAPIResponse(const ResponseData: string; StatusCode: Integer);
    procedure LogMouseAction(const Action: string; X, Y: Integer);

    property Enabled: Boolean read FEnabled write FEnabled;
    property LogLevel: TLogLevel read FLogLevel write FLogLevel;
  end;

var
  Logger: TLogger;

implementation

uses
  System.DateUtils;

{ TLogger }

constructor TLogger.Create;
begin
  inherited Create;
  FLock := TCriticalSection.Create;
  FLogStream := nil;
  FMaxFileSize := 10 * 1024 * 1024; // 10MB
  FEnabled := True;
  FLogLevel := llInfo;
end;

destructor TLogger.Destroy;
begin
  if Assigned(FLogStream) then
    FLogStream.Free;
  FLock.Free;
  inherited Destroy;
end;

procedure TLogger.Initialize(const LogFile: string; const LogLevel: string; Enabled: Boolean);
var
  LogDir: string;
begin
  FLock.Enter;
  try
    FEnabled := Enabled;
    FLogLevel := StringToLogLevel(LogLevel);

    if FEnabled and (LogFile <> '') then
    begin
      FLogFile := LogFile;

      // Cria o diretório se não existir
      LogDir := ExtractFilePath(FLogFile);
      if (LogDir <> '') and not TDirectory.Exists(LogDir) then
        TDirectory.CreateDirectory(LogDir);

      // Abre o arquivo de log
      try
        if Assigned(FLogStream) then
          FLogStream.Free;

        FLogStream := TFileStream.Create(FLogFile, fmCreate or fmOpenWrite or fmShareDenyWrite);

        // Escreve cabeçalho
        WriteToFile('=== SolveCaptchaV3 Log Started ===');
        WriteToFile(Format('Timestamp: %s', [FormatDateTime('yyyy-mm-dd hh:nn:ss', Now)]));
        WriteToFile(Format('Log Level: %s', [LogLevelToString(FLogLevel)]));
        WriteToFile('=====================================');
      except
        on E: Exception do
        begin
          FEnabled := False;
          // Não pode logar aqui pois estamos no próprio logger
        end;
      end;
    end;
  finally
    FLock.Leave;
  end;
end;

function TLogger.LogLevelToString(Level: TLogLevel): string;
begin
  case Level of
    llDebug: Result := 'DEBUG';
    llInfo: Result := 'INFO';
    llWarning: Result := 'WARNING';
    llError: Result := 'ERROR';
  else
    Result := 'UNKNOWN';
  end;
end;

function TLogger.StringToLogLevel(const LevelStr: string): TLogLevel;
var
  UpperLevel: string;
begin
  UpperLevel := UpperCase(Trim(LevelStr));
  if UpperLevel = 'DEBUG' then
    Result := llDebug
  else if UpperLevel = 'INFO' then
    Result := llInfo
  else if UpperLevel = 'WARNING' then
    Result := llWarning
  else if UpperLevel = 'ERROR' then
    Result := llError
  else
    Result := llInfo; // Padrão
end;

procedure TLogger.WriteToFile(const Message: string);
var
  LogLine: string;
  LogBytes: TBytes;
begin
  if not FEnabled or not Assigned(FLogStream) then
    Exit;

  LogLine := Format('[%s] %s%s', [
    FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz', Now),
    Message,
    sLineBreak
  ]);

  LogBytes := TEncoding.UTF8.GetBytes(LogLine);
  FLogStream.WriteBuffer(LogBytes[0], Length(LogBytes));
  // FLogStream.Flush; // Flush não está disponível em TFileStream

  CheckFileSize;
end;

procedure TLogger.CheckFileSize;
begin
  if Assigned(FLogStream) and (FLogStream.Size > FMaxFileSize) then
    RotateLogFile;
end;

procedure TLogger.RotateLogFile;
var
  BackupFile: string;
begin
  if not Assigned(FLogStream) then
    Exit;

  try
    FLogStream.Free;
    FLogStream := nil;

    // Cria backup do arquivo atual
    BackupFile := ChangeFileExt(FLogFile, '.bak');
    if TFile.Exists(BackupFile) then
      TFile.Delete(BackupFile);
    TFile.Move(FLogFile, BackupFile);

    // Cria novo arquivo
    FLogStream := TFileStream.Create(FLogFile, fmCreate or fmOpenWrite or fmShareDenyWrite);
    WriteToFile('=== Log Rotated ===');
  except
    on E: Exception do
      FEnabled := False;
  end;
end;

procedure TLogger.Log(Level: TLogLevel; const Message: string);
var
  FormattedMessage: string;
begin
  if not FEnabled or (Level < FLogLevel) then
    Exit;

  FLock.Enter;
  try
    FormattedMessage := Format('[%s] %s', [LogLevelToString(Level), Message]);
    WriteToFile(FormattedMessage);
  finally
    FLock.Leave;
  end;
end;

procedure TLogger.Log(Level: TLogLevel; const Format: string; const Args: array of const);
begin
  Log(Level, System.SysUtils.Format(Format, Args));
end;

procedure TLogger.Debug(const Message: string);
begin
  Log(llDebug, Message);
end;

procedure TLogger.Debug(const Format: string; const Args: array of const);
begin
  Log(llDebug, Format, Args);
end;

procedure TLogger.Info(const Message: string);
begin
  Log(llInfo, Message);
end;

procedure TLogger.Info(const Format: string; const Args: array of const);
begin
  Log(llInfo, Format, Args);
end;

procedure TLogger.Warning(const Message: string);
begin
  Log(llWarning, Message);
end;

procedure TLogger.Warning(const Format: string; const Args: array of const);
begin
  Log(llWarning, Format, Args);
end;

procedure TLogger.Error(const Message: string);
begin
  Log(llError, Message);
end;

procedure TLogger.Error(const Format: string; const Args: array of const);
begin
  Log(llError, Format, Args);
end;

procedure TLogger.LogCaptchaStart(const URL: string);
begin
  Info('=== CAPTCHA Resolution Started ===');
  Info('Target URL: %s', [URL]);
end;

procedure TLogger.LogCaptchaSuccess(const Action: string; Confidence: Double; TimeTaken: Integer);
begin
  Info('=== CAPTCHA Resolution Successful ===');
  Info('Action: %s', [Action]);
  Info('Confidence: %.2f%%', [Confidence * 100]);
  Info('Time Taken: %d ms', [TimeTaken]);
end;

procedure TLogger.LogCaptchaFailure(const Reason: string; TimeTaken: Integer);
begin
  Warning('=== CAPTCHA Resolution Failed ===');
  Warning('Reason: %s', [Reason]);
  Warning('Time Taken: %d ms', [TimeTaken]);
end;

procedure TLogger.LogAPIRequest(const RequestData: string);
begin
  Debug('API Request: %s', [RequestData]);
end;

procedure TLogger.LogAPIResponse(const ResponseData: string; StatusCode: Integer);
begin
  if StatusCode = 200 then
    Debug('API Response [%d]: %s', [StatusCode, ResponseData])
  else
    Warning('API Response [%d]: %s', [StatusCode, ResponseData]);
end;

procedure TLogger.LogMouseAction(const Action: string; X, Y: Integer);
begin
  Debug('Mouse Action: %s at (%d, %d)', [Action, X, Y]);
end;

initialization
  Logger := TLogger.Create;
  // Inicialização será feita após carregar configurações

finalization
  Logger.Free;

end.
