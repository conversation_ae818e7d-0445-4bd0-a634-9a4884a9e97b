MfPack X 3.1.7

/* Recommended project settings */


/* Project output */
.\$(Platform)\$(Config)

/* Unit output */
.\$(Platform)\$(Config)



/* MfPack description */

/* Media Foundation */

Microsoft Media Foundation is part of the brand new platforms to play, record, stream etc. multimedia of any kind.

MFPack is the Delphi version of Microsoft Media Foundation, Core Audio etc. for the Windows Operating System (OS)

/* This is what MfPack covers: */

/* Microsoft Media Foundation: */ 
   An end-to-end media pipeline, which supports playback, audio/video capture, 
   and encoding (successor to DirectShow).

/* Windows Media Library Sharing Services: */ 
   Enables applications to discover media devices on the home network, and share media libraries on the 
   home network and the Internet.

/* Core Audio APIs: */ 
   A low-level API for audio capture and audio rendering, which can be used to achieve minimum latency or to 
   implement features that might not be entirely supported by higher-level media APIs.

/* Multimedia Device (MMDevice) API. */ 
   Clients use this API to enumerate the audio endpoint devices in the system.

/* Windows Audio Session API (WASAPI). */ 
   Clients use this API to create and manage audio streams to and from audio endpoint devices. 
   Keep in mind that this API is designed for realtime audio processing. 
   For this, compressed media is not supported, only raw audio formats, 
   primarily the Waveform Audio Format (WAV/WAVE) can be processed form endpoint to endpoint, including files.

/* DeviceTopology API. */ 
  Clients use this API to directly access the topological features 
  (for example, volume controls and multiplexers) that lie along the data paths inside hardware devices in audio adapters.

/* EndpointVolume API. */ 
   Clients use this API to directly access the volume controls on audio endpoint devices. 
   This API is primarily used by applications that manage exclusive-mode audio streams.

/* Windows Imaging Component */

/* WIC API */

/* DirectX The folllowing API's are included, to support Media Foundation. */

   - D2D1 API
   - DirectComposition API
   - DirectWrite API
   - DXGI API
   - DXVA API
   - XAudio2 API
   - D3D9 API
   - D3D11 API
   - D3D12 API


/* Windows Media The full Windows Media Api. */ 
   This API has been added to MfPack to support the Core Audio API. 
   Note: Delphi's implementation (MMSystem) is just a sub-set of this Api.

/* about */ 
  DirectShow, DirectSound and DirectX DirectShow and Clootie DirectX platforms are not included with MfPack.
  DirectShow and Clootie DirectX are not necessarily needed for MfPack, except for some interfaces that are
  not yet translated to the latest API's or a sample like DuckingMediaPlayer.

  Until Windows 10, both platforms will be operational within the Windows family (that is what MS says). 
  If you're not intended to develop applications that rely on Media Foundation or you are a happy owner of a 
  Delphi version that does not includes the translations of DirectShow, DirectSound and DirectX, 
  then we advise you to get the latest DirectShow, DirectSound and DirectX versions: 
  Clootie and DSPack (SourceForce) or for a maintained version up to Delphi XE8 DSPack (Github).


/* Project Members/ Contributors: */
  Ciaran
  FactoryX (admin)
  Ian Krigsman
  Markus (MBulli)
  Tilo Güldner
  Tony Kalf
  TopPlay

/* Project Members/ Initiators: */
Peter Larson (OzShips)
Tony Kalf (maXcomX)

<EOF>