# SinkWriterToEncodeVideoSamples

Version: X 3.1.7

Description:
  These samples demonstrates how to use the SinkWriter to encode a video file from a bitmap.
  This sample has 3 levels:
  - Example 1 creates a simple green bitmap (640x480) and store it to a file with a length of 20 seconds.
  - Example 2 demonstrates how to use the SinkWriter to create a video from one or more bitmap files.
  - Example 3 demonstrates how to use the SinkWriter to create a video from one or more imagefiles, 
    like jpg, bitmap, gif or png files. It also provides an option to add audio and define it's codec (AAC, FLAC and Dolby AC-3).
 

Supported output format is MP4.

NOTES:
 - This release is updated for compiler version 17 up to 34.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 10 or later.
 - Minimum supported MfPack version: 3.1.6

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 25-11-2022
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.




