object MainForm: TMainForm
  Left = 0
  Top = 0
  Caption = 'SolveCaptchaV3 - WindowlessBrowser'
  ClientHeight = 600
  ClientWidth = 800
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 15
  object AddressPnl: TPanel
    Left = 0
    Top = 0
    Width = 800
    Height = 30
    Align = alTop
    BevelOuter = bvNone
    Enabled = False
    TabOrder = 0
    object AddressCb: TComboBox
      Left = 8
      Top = 4
      Width = 700
      Height = 23
      ItemIndex = 0
      TabOrder = 0
      Text = 'https://accounts.hcaptcha.com/demo'
      Items.Strings = (
        'https://accounts.hcaptcha.com/demo'
        'https://smartapi.tech/token/mousemov.php'
        'https://smartapi.tech/token/click.php')
    end
    object GoBtn: TButton
      Left = 720
      Top = 4
      Width = 70
      Height = 23
      Caption = 'Go'
      TabOrder = 1
      OnClick = GoBtnClick
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 500
    Width = 800
    Height = 100
    Align = alBottom
    TabOrder = 1
    object BtnMHTML: TButton
      Left = 8
      Top = 8
      Width = 90
      Height = 25
      Caption = 'Get MHTML'
      TabOrder = 0
      OnClick = BtnMHTMLClick
    end
    object BtnTesteClick: TButton
      Left = 110
      Top = 8
      Width = 75
      Height = 25
      Caption = 'Test Click'
      TabOrder = 1
      OnClick = BtnTesteClickClick
    end
    object BtnTesteMover: TButton
      Left = 195
      Top = 8
      Width = 75
      Height = 25
      Caption = 'Test Move'
      TabOrder = 2
      OnClick = BtnTesteMoverClick
    end
    object BtnClickHCJs: TButton
      Left = 280
      Top = 8
      Width = 120
      Height = 25
      Caption = 'Click hCAPTCHA (JS)'
      TabOrder = 3
      OnClick = BtnClickHCJsClick
    end
    object BtnClickHCWebview: TButton
      Left = 410
      Top = 8
      Width = 130
      Height = 25
      Caption = 'Click hCAPTCHA (WV)'
      TabOrder = 4
      OnClick = BtnClickHCWebviewClick
    end
    object BtnSolveCaptcha: TButton
      Left = 550
      Top = 8
      Width = 120
      Height = 25
      Caption = 'Solve Captcha'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 5
      OnClick = BtnSolveCaptchaClick
    end
    object Button1: TButton
      Left = 680
      Top = 8
      Width = 75
      Height = 25
      Caption = 'Test'
      TabOrder = 6
      OnClick = Button1Click
    end
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 300
    OnTimer = Timer1Timer
    Left = 200
    Top = 100
  end
  object WVBrowser1: TWVBrowser
    TargetCompatibleBrowserVersion = '95.0.1020.44'
    AllowSingleSignOnUsingOSPrimaryAccount = False
    OnInitializationError = WVBrowser1InitializationError
    OnAfterCreated = WVBrowser1AfterCreated
    OnExecuteScriptCompleted = WVBrowser1ExecuteScriptCompleted
    OnDocumentTitleChanged = WVBrowser1DocumentTitleChanged
    OnWebMessageReceived = WVBrowser1WebMessageReceived
    OnCursorChanged = WVBrowser1CursorChanged
    OnRetrieveMHTMLCompleted = WVBrowser1RetrieveMHTMLCompleted
    OnContextMenuRequested = WVBrowser1ContextMenuRequested
    OnCustomItemSelected = WVBrowser1CustomItemSelected
    Left = 300
    Top = 100
  end
  object ApplicationEvents1: TApplicationEvents
    OnMessage = ApplicationEvents1Message
    Left = 400
    Top = 100
  end
end
