unit uWindowlessBrowser;

{$IFDEF WEBVIEW4DELPHI}
  {$I webview2.inc}
{$ENDIF}

interface

uses
  Winapi.Windows, Winapi.Messages, WinApi.ActiveX, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls,
  Vcl.ComCtrls, Vcl.StdCtrls, Vcl.AppEvnts,
  uWVBrowser, uWVWinControl, uWVWindowParent, uWVTypes, uWVConstants, uWVTypeLibrary,
  uWVLibFunctions, uWVLoader, uWVInterfaces, uWVCoreWebView2Args, uWVBrowserBase,
  uWVCoreWebView2ContextMenuItemCollection, uWVCoreWebView2ContextMenuItem,
  uDirectCompositionHost, math, Vcl.Samples.Spin,
  System.Net.HttpClient, System.Net.URLClient, System.Net.Mime,
  uConfig, uLogger, System.Diagnostics;

type
  TCaptchaResponse = record
    success: Boolean;
    action: string;
    target: TPoint;
    message: string;
    confidence: Double;
  end;

  TGPTRequest = record
    model: string;
    messages: array of record
      role: string;
      content: string;
    end;
    max_tokens: Integer;
    temperature: Double;
  end;

  TMainForm = class(TForm, IDropTarget)
    Timer1: TTimer;
    WVBrowser1: TWVBrowser;
    AddressPnl: TPanel;
    AddressCb: TComboBox;
    GoBtn: TButton;
    ApplicationEvents1: TApplicationEvents;
    Panel1: TPanel;
    MMHTML: TMemo;
    BtnMHTML: TButton;
    BtnTesteClick: TButton;
    BtnTesteMover: TButton;
    BtnClickHCJs: TButton;
    BtnClickHCWebview: TButton;
    BtnSolveCaptcha: TButton;

    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormShow(Sender: TObject);

    procedure Timer1Timer(Sender: TObject);
    procedure GoBtnClick(Sender: TObject);
    procedure ApplicationEvents1Message(var Msg: tagMSG; var Handled: Boolean);

    procedure WVBrowser1AfterCreated(Sender: TObject);
    procedure WVBrowser1DocumentTitleChanged(Sender: TObject);
    procedure WVBrowser1InitializationError(Sender: TObject; aErrorCode: HRESULT; const aErrorMessage: wvstring);
    procedure WVBrowser1CursorChanged(Sender: TObject);
    procedure WVBrowser1ContextMenuRequested(Sender: TObject; const aWebView: ICoreWebView2; const aArgs: ICoreWebView2ContextMenuRequestedEventArgs);
    procedure WVBrowser1CustomItemSelected(Sender: TObject; const aMenuItem: ICoreWebView2ContextMenuItem);
    procedure WVBrowser1WebMessageReceived(Sender: TObject; const aWebView: ICoreWebView2; const aArgs: ICoreWebView2WebMessageReceivedEventArgs);
    procedure BtnMHTMLClick(Sender: TObject);
    procedure WVBrowser1RetrieveMHTMLCompleted(Sender: TObject;
      aResult: Boolean; const aMHTML: wvstring);
    procedure BtnTesteClickClick(Sender: TObject);
    procedure BtnTesteMoverClick(Sender: TObject);
    procedure BtnClickHCJsClick(Sender: TObject);
    procedure WVBrowser1ExecuteScriptCompleted(Sender: TObject;
      aErrorCode: HRESULT; const aResultObjectAsJson: wvstring;
      aExecutionID: Integer);
    procedure BtnClickHCWebviewClick(Sender: TObject);
    procedure Button1Click(Sender: TObject);

  protected
    FWVDirectCompositionHost : TWVDirectCompositionHost;
    FIsCapturingMouse        : boolean;
    FIsTrackingMouse         : boolean;
    FDragAndDropInitialized  : boolean;
    bExecutouScript          : Boolean;
    FLastScriptResult        : string;
    FStopwatch              : TStopwatch;

    function HandleMouseMessage(aMessage : cardinal; aWParam : WPARAM; aLParam : LPARAM) : boolean;
    function TrackMouseEvents(aMouseTrackingFlags : cardinal) : boolean;
    function OffsetPointToWebView(aPoint : TPoint) : TPoint;

  public
    FExecJSCommandID : integer;
    FExecJSMenuItem  : TCoreWebView2ContextMenuItem;

    // IDropTarget
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function IDropTarget.DragOver = IDropTarget_DragOver;
    function IDropTarget_DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;

    procedure InitializeDragAndDrop;
    procedure ShutdownDragAndDrop;

    //para mover o mouse
    procedure MoveMouseTo(WebView: TWVBrowser; TargetX, TargetY:integer);
    //para clicar
    procedure Click(WebView: TWVBrowser; TargetX, TargetY:integer);
    procedure MoveHcaptcha;
    procedure BtnSolveCaptchaClick(Sender: TObject);
    function SendToGPT4OMini(const MHTML: string): TCaptchaResponse;
    function DetectCaptchaType: string;
    procedure ExecuteCaptchaAction(const Response: TCaptchaResponse);
    function AnalyzeCaptchaWithGPT(const HTMLContent: string): TCaptchaResponse;
    function ExtractCaptchaInfo(const HTMLContent: string): string;
  end;

var
  MainForm: TMainForm;

implementation

{$R *.dfm}

uses
  System.JSON,
  uWVMiscFunctions;

// This is a demo of a WebView2 browser in "Windowsless mode" using WebView4Delphi.
// https://github.com/MicrosoftEdge/WebView2Feedback/issues/20

// The Windowless mode uses the DirectComposition API :
// https://docs.microsoft.com/en-us/windows/win32/directcomp/directcomposition-portal

// At this moment Delphi doesn't support the DirectComposition API so we have to use the
// MfPack component available at GitHub :
// https://github.com/FactoryXCode/MfPack

// It's necessary to add the MfPack source directory to the search path of this demo.

// In order to avoid adding a dependency to WebView4Delphi we create a
// TWVDirectCompositionHost instance at runtime.

// The code in this demo is almost a direct translation of the code in the official
// WebView2APISample available at the WebView2Samples repository :
// https://github.com/MicrosoftEdge/WebView2Samples/tree/master/SampleApps/WebView2APISample

// TO-DO : Add support for touch devices.

procedure TMainForm.ApplicationEvents1Message(var Msg: tagMSG; var Handled: Boolean);
begin
  case Msg.message of
    WM_SIZE :
      case Msg.wParam of
        SIZE_MINIMIZED :
          begin
            WVBrowser1.IsVisible := False;
            WVBrowser1.TrySuspend;
          end;

        SIZE_RESTORED :
          begin
            WVBrowser1.Resume;
            WVBrowser1.IsVisible := True;
          end;
      end;

    WM_MOVE,
    WM_MOVING :
      if (WVBrowser1 <> nil) then
        WVBrowser1.NotifyParentWindowPositionChanged;

    WM_MOUSELEAVE,
    WM_MOUSEFIRST..WM_MOUSELAST :
      HandleMouseMessage(Msg.message, Msg.wParam, Msg.lParam);
  end;
end;

function TMainForm.HandleMouseMessage(aMessage : cardinal; aWParam : WPARAM; aLParam : LPARAM) : boolean;
var
  TempPoint : TPoint;
  TempInClientRect : boolean;
  TempMouseData : cardinal;
  TempKeyState : integer;
begin
  Result := False;

  if not(assigned(FWVDirectCompositionHost)) then exit;

  TempPoint.x := int16(aLParam and $FFFF);
  TempPoint.y := int16((aLParam and $FFFF0000) shr 16);

  if (aMessage = WM_MOUSEWHEEL)  or
     (aMessage = WM_MOUSEHWHEEL) then
    TempPoint := FWVDirectCompositionHost.ScreenToclient(TempPoint);

  TempInClientRect := PtInRect(FWVDirectCompositionHost.ClientRect, TempPoint);

  if TempInClientRect or (aMessage = WM_MOUSELEAVE) or FIsCapturingMouse then
    begin
      TempMouseData := 0;
      TempKeyState  := int16(aWParam and $FFFF);

      case aMessage of
        WM_MOUSEWHEEL,
        WM_MOUSEHWHEEL,
        WM_XBUTTONDBLCLK,
        WM_XBUTTONDOWN,
        WM_XBUTTONUP :
          TempMouseData := cardinal((aWParam and $FFFF0000) shr 16);

        WM_MOUSEMOVE :
          if not(FIsTrackingMouse) then
            begin
              TrackMouseEvents(TME_LEAVE);
              FIsTrackingMouse := True;
            end;

        WM_MOUSELEAVE :
          FIsTrackingMouse := False;
      end;

      case aMessage of
        WM_LBUTTONDOWN,
        WM_MBUTTONDOWN,
        WM_RBUTTONDOWN,
        WM_XBUTTONDOWN :
          if TempInClientRect and (GetCapture <> FWVDirectCompositionHost.Handle) then
            begin
              FIsCapturingMouse := True;
              SetCapture(FWVDirectCompositionHost.Handle);
            end;

        WM_LBUTTONUP,
        WM_MBUTTONUP,
        WM_RBUTTONUP,
        WM_XBUTTONUP :
          if (GetCapture = FWVDirectCompositionHost.Handle) then
            begin
              FIsCapturingMouse := False;
              ReleaseCapture;
            end;
      end;

      Result := WVBrowser1.SendMouseInput(TWVMouseEventKind(aMessage),
                                          TWVMouseEventVirtualKeys(TempKeyState),
                                          TempMouseData,
                                          TempPoint);
    end
   else
    if (aMessage = WM_MOUSEMOVE) and FIsTrackingMouse then
      begin
        FIsTrackingMouse := False;
        TrackMouseEvents(TME_LEAVE or TME_CANCEL);
        HandleMouseMessage(WM_MOUSELEAVE, 0, 0);
      end;
end;

function TMainForm.TrackMouseEvents(aMouseTrackingFlags : cardinal) : boolean;
var
  TempEvent : TTRACKMOUSEEVENT;
begin
  TempEvent.cbSize      := SizeOf(TTRACKMOUSEEVENT);
  TempEvent.dwFlags     := aMouseTrackingFlags;
  TempEvent.hwndTrack   := FWVDirectCompositionHost.Handle;
  TempEvent.dwHoverTime := 0;

  Result := TrackMouseEvent(TempEvent);
end;

procedure TMainForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  WVBrowser1.RootVisualTarget := nil;
  FWVDirectCompositionHost.DestroyDCompVisualTree;
  ShutdownDragAndDrop;
end;

procedure TMainForm.FormCreate(Sender: TObject);
begin
  bExecutouScript                  :=False;
  FExecJSCommandID                 := 0;
  FExecJSMenuItem                  := nil;
  FIsCapturingMouse                := False;
  FIsTrackingMouse                 := False;
  FDragAndDropInitialized          := False;
  FStopwatch                       := TStopwatch.Create;

  // Inicializa o sistema de logging
  Logger.Initialize(AppConfig.GetLogFilePath, AppConfig.Logging.LogLevel, AppConfig.Logging.EnableLogging);
  Logger.Info('=== SolveCaptchaV3 Started ===');
  Logger.Info('Application Version: 3.0');
  Logger.Info('Configuration loaded from: %s', [AppConfig.FConfigFile]);

  // Verifica se a API key está configurada
  if not AppConfig.IsAPIKeyValid then
  begin
    Logger.Warning('OpenAI API Key not configured properly');
    ShowMessage('ATENÇÃO: Chave API da OpenAI não configurada!' + sLineBreak +
                'Configure sua chave no arquivo config.ini antes de usar o solver.');
  end;

  WVBrowser1.DefaultURL            := AddressCb.Text;

  FWVDirectCompositionHost         := TWVDirectCompositionHost.Create(self);
  FWVDirectCompositionHost.Parent  := self;
  FWVDirectCompositionHost.Align   := alClient;
  FWVDirectCompositionHost.Browser := WVBrowser1;
  FWVDirectCompositionHost.CreateHandle;

  Logger.Info('WebView2 DirectComposition Host created');
end;

procedure TMainForm.FormDestroy(Sender: TObject);
begin
  if assigned(FExecJSMenuItem) then
    FreeAndNil(FExecJSMenuItem);
end;

procedure TMainForm.FormShow(Sender: TObject);
begin
  if GlobalWebView2Loader.InitializationError then
    showmessage(GlobalWebView2Loader.ErrorMessage)
   else
    if GlobalWebView2Loader.Initialized then
      WVBrowser1.CreateWindowlessBrowser(FWVDirectCompositionHost.Handle)
     else
      Timer1.Enabled := True;
end;

procedure TMainForm.GoBtnClick(Sender: TObject);
begin
  WVBrowser1.Navigate(AddressCb.Text);
end;

procedure TMainForm.WVBrowser1AfterCreated(Sender: TObject);
begin
  if FWVDirectCompositionHost.BuildDCompTreeUsingVisual then
    begin
      // Simplified DirectComposition - no visual target needed
      // WVBrowser1.RootVisualTarget := FWVDirectCompositionHost.WebViewVisual;
      // FWVDirectCompositionHost.DCompDevice.Commit;
    end;

  FWVDirectCompositionHost.UpdateSize;
  FWVDirectCompositionHost.SetFocus;

  WVBrowser1.AllowExternalDrop := True;

  InitializeDragAndDrop;

  Caption := 'WindowlessBrowser';
  AddressPnl.Enabled := True;
end;

procedure TMainForm.WVBrowser1ContextMenuRequested(Sender: TObject;
  const aWebView: ICoreWebView2;
  const aArgs: ICoreWebView2ContextMenuRequestedEventArgs);
var
  TempArgs        : TCoreWebView2ContextMenuRequestedEventArgs;
  TempCollection  : TCoreWebView2ContextMenuItemCollection;
  TempMenuItemItf : ICoreWebView2ContextMenuItem;
begin
  TempArgs       := TCoreWebView2ContextMenuRequestedEventArgs.Create(aArgs);
  TempCollection := TCoreWebView2ContextMenuItemCollection.Create(TempArgs.MenuItems);

  try
    if not(Assigned(FExecJSMenuItem)) then
      begin
        if WVBrowser1.CoreWebView2Environment.CreateContextMenuItem('Execute custom JavaScript...', nil, COREWEBVIEW2_CONTEXT_MENU_ITEM_KIND_COMMAND, TempMenuItemItf) then
          try
            FExecJSMenuItem   := TCoreWebView2ContextMenuItem.Create(TempMenuItemItf);
            FExecJSCommandID  := FExecJSMenuItem.CommandId;
            FExecJSMenuItem.AddAllBrowserEvents(WVBrowser1);
          finally
            TempMenuItemItf := nil;
          end;
      end;

    if assigned(FExecJSMenuItem) and FExecJSMenuItem.Initialized then
      TempCollection.InsertValueAtIndex(TempCollection.Count, FExecJSMenuItem.BaseIntf);
  finally
    FreeAndNil(TempCollection);
    FreeAndNil(TempArgs);
  end;
end;

procedure TMainForm.WVBrowser1CursorChanged(Sender: TObject);
begin
  FWVDirectCompositionHost.Cursor := SystemCursorIDToDelphiCursor(WVBrowser1.SystemCursorId);
end;

procedure TMainForm.WVBrowser1CustomItemSelected(Sender: TObject;
  const aMenuItem: ICoreWebView2ContextMenuItem);
var
  TempMenuItem : TCoreWebView2ContextMenuItem;
begin
  TempMenuItem := TCoreWebView2ContextMenuItem.Create(aMenuItem);

  if (TempMenuItem.CommandId = FExecJSCommandID) then
    TThread.ForceQueue(nil,
      procedure
      var
        TempCode : string;
      begin
        TempCode := 'var myElement = document.getElementById(' + quotedstr('keywords') + ');' + CRLF +
                    'var myRect = myElement.getBoundingClientRect();' + CRLF +
                    'window.chrome.webview.postMessage(myRect.toJSON());';
        TempCode := trim(InputBox('Execute JavaScript', 'JavaScript code', TempCode));

        if (TempCode <> '') then
          WVBrowser1.ExecuteScript(TempCode);
      end);

  FreeAndNil(TempMenuItem);
end;

procedure TMainForm.WVBrowser1DocumentTitleChanged(Sender: TObject);
begin
  Caption := 'WindowlessBrowser - ' + WVBrowser1.DocumentTitle;
end;

procedure TMainForm.WVBrowser1ExecuteScriptCompleted(Sender: TObject;
  aErrorCode: HRESULT; const aResultObjectAsJson: wvstring;
  aExecutionID: Integer);
begin
  bExecutouScript := True;
  FLastScriptResult := aResultObjectAsJson;
end;

procedure TMainForm.WVBrowser1InitializationError(Sender: TObject;
  aErrorCode: HRESULT; const aErrorMessage: wvstring);
begin
  showmessage(aErrorMessage);
end;

procedure TMainForm.WVBrowser1RetrieveMHTMLCompleted(Sender: TObject;
  aResult: Boolean; const aMHTML: wvstring);
begin
  MMHTML.Lines.Text:=aMHTML;
end;

procedure TMainForm.WVBrowser1WebMessageReceived(Sender: TObject;
  const aWebView: ICoreWebView2;
  const aArgs: ICoreWebView2WebMessageReceivedEventArgs);
{$IFDEF DELPHI26_UP}
var
  TempArgs   : TCoreWebView2WebMessageReceivedEventArgs;
  TempMsg    : string;
  TempObject : TJSonObject;
  TempValue  : TJSonValue;
  TempPoint  : TPoint;
  TempSize   : TSize;
  TempScale  : single;
{$ELSE}
  // TO-DO: Use an alternative way to parse the JSON message in Delphi 10.2.3 Tokio or older
{$ENDIF}
begin
{$IFDEF DELPHI26_UP}
  TempArgs := TCoreWebView2WebMessageReceivedEventArgs.Create(aArgs);
  TempMsg  := TempArgs.WebMessageAsJson;

  // The JavaScript code returned a DOMRect in JSON format.
  TempObject  := TJSonObject.Create;
  TempValue   := TempObject.ParseJSONValue(TempMsg);
  TempScale   := WVBrowser1.ScreenScale;

  // Get the coordinates and size of the element
  TempPoint.x := round((TempValue as TJSONObject).Get('x').JSONValue.AsType<double> * TempScale);
  TempPoint.y := round((TempValue as TJSONObject).Get('y').JSONValue.AsType<double> * TempScale);
  TempSize.cx := round((TempValue as TJSONObject).Get('width').JSONValue.AsType<double> * TempScale);
  TempSize.cy := round((TempValue as TJSONObject).Get('height').JSONValue.AsType<double> * TempScale);

  // Middle point of the element
  TempPoint.x := TempPoint.x + (TempSize.cx div 2);
  TempPoint.y := TempPoint.y + (TempSize.cy div 2);

  // Simulate a left mouse button down
  WVBrowser1.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_DOWN,
                            COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_LEFT_BUTTON,
                            0,
                            TempPoint);

  // Simulate a left mouse button up to complete a simulated click on the element
  WVBrowser1.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_UP,
                            COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_NONE,
                            0,
                            TempPoint);

  TempArgs.Free;
  TempObject.Free;
{$ELSE}
  // TO-DO: Use an alternative way to parse the JSON message in Delphi 10.2.3 Tokio or older
{$ENDIF}
end;

procedure TMainForm.Timer1Timer(Sender: TObject);
begin
  Timer1.Enabled := False;

  if GlobalWebView2Loader.Initialized then
    WVBrowser1.CreateWindowlessBrowser(FWVDirectCompositionHost.Handle)
   else
    Timer1.Enabled := True;
end;

procedure TMainForm.InitializeDragAndDrop;
begin
  if not(FDragAndDropInitialized) then
    FDragAndDropInitialized := succeeded(RegisterDragDrop(Handle, self));
end;

procedure TMainForm.MoveHcaptcha;
begin
var Script:String :=
    '(function() {' + sLineBreak +
    '  var iframe = document.querySelector(''iframe[src*="hcaptcha.com"]'');' + sLineBreak +
    '  var container = iframe ? iframe.parentElement : document.querySelector(''[title="Widget contendo caixa de sele��o para desafio de seguran�a hCaptcha"]'');' + sLineBreak +
    '  if (container) {' + sLineBreak +
    '    var styles = {' + sLineBreak +
    '      position: "fixed",' + sLineBreak +
    '      left: "230px",' + sLineBreak +
    '      top: "378px",' + sLineBreak +
    '      zIndex: "9999",' + sLineBreak +
    '      width: "303px",' + sLineBreak +
    '      height: "78px",' + sLineBreak +
    '      padding: "0px"' + sLineBreak +
    '    };' + sLineBreak +
    '    Object.keys(styles).forEach(function(property) {' + sLineBreak +
    '      container.style[property] = styles[property];' + sLineBreak +
    '    });' + sLineBreak +
    '  }' + sLineBreak +
    '})();';

  bExecutouScript:=False;
  WVBrowser1.ExecuteScript(Script);
  while not bExecutouScript do
   begin
     Application.ProcessMessages;
     Sleep(100);
   end;
end;

procedure TMainForm.MoveMouseTo(WebView: TWVBrowser; TargetX, TargetY: integer);
 var TempPoint  : TPoint;
begin
  TempPoint.x := TargetX;
  TempPoint.y := TargetY;

  WebView.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_MOVE,
                         COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_NONE,
                         0,TempPoint);
end;

procedure TMainForm.ShutdownDragAndDrop;
begin
  if FDragAndDropInitialized then
    RevokeDragDrop(Handle);
end;


procedure TMainForm.BtnClickHCJsClick(Sender: TObject);
begin
  var JavaScriptCode: string :=
    'var iframes = document.getElementsByTagName("iframe");' + sLineBreak +
    'if (iframes.length === 0) {' + sLineBreak +
    '    console.log("Nenhum iframe encontrado na p�gina.");' + sLineBreak +
    '} else {' + sLineBreak +
    '    Array.from(iframes).forEach(function(iframe) {' + sLineBreak +
    '        try {' + sLineBreak +
    '            if (iframe.src.includes("hcaptcha")) {' + sLineBreak +
    '                console.log("Iframe encontrado com src contendo ''hcaptcha'':", iframe.src);' + sLineBreak +
    '                var iframeDocument = iframe.contentDocument || iframe.contentWindow.document;' + sLineBreak +
    '                if (iframeDocument) {' + sLineBreak +
    '                    console.log("Conte�do do iframe acessado:", iframe.src);' + sLineBreak +
    '                    var checkbox = iframeDocument.getElementById("checkbox");' + sLineBreak +
    '                    if (checkbox && !checkbox.checked) {' + sLineBreak +
    '                        console.log("Checkbox encontrado. Clicando...");' + sLineBreak +
    '                        checkbox.click();' + sLineBreak +
    '                    } else if (checkbox) {' + sLineBreak +
    '                        console.log("Checkbox j� est� marcado no iframe:", iframe.src);' + sLineBreak +
    '                    } else {' + sLineBreak +
    '                        console.log("Checkbox com id=''checkbox'' n�o encontrado no iframe:", iframe.src);' + sLineBreak +
    '                    }' + sLineBreak +
    '                } else {' + sLineBreak +
    '                    console.log("Conte�do do iframe ainda n�o acess�vel:", iframe.src);' + sLineBreak +
    '                }' + sLineBreak +
    '            } else {' + sLineBreak +
    '                console.log("Iframe ignorado, pois o src n�o cont�m ''hcaptcha'':", iframe.src);' + sLineBreak +
    '            }' + sLineBreak +
    '        } catch (e) {' + sLineBreak +
    '            console.error("Erro ao acessar conte�do do iframe:", e);' + sLineBreak +
    '        }' + sLineBreak +
    '    });' + sLineBreak +
    '}';

   WVBrowser1.ExecuteScript(JavaScriptCode);
end;

procedure TMainForm.BtnClickHCWebviewClick(Sender: TObject);
begin
  MoveHcaptcha;
  Click(WVBrowser1, 310, 500);
end;

procedure TMainForm.BtnMHTMLClick(Sender: TObject);
begin
  if not WVBrowser1.RetrieveMHTML then
    ShowMessage('Falha ao obter MHTML.');
end;

procedure TMainForm.BtnTesteClickClick(Sender: TObject);
begin
  if WVBrowser1.Source <> 'https://smartapi.tech/token/click.php' then
   begin
     WVBrowser1.Navigate('https://smartapi.tech/token/click.php');
     ShowMessage('Alterando a p�gina para teste de click');
     exit;
   end;

  Click(WVBrowser1, RandomRange(0, 200), RandomRange(0, 200));
end;

procedure TMainForm.BtnTesteMoverClick(Sender: TObject);
 var iX:Integer;
begin
  if WVBrowser1.Source <> 'https://smartapi.tech/token/mousemov.php' then
   begin
     WVBrowser1.Navigate('https://smartapi.tech/token/mousemov.php');
     ShowMessage('Alterando a p�gina para teste de movimento');
     exit;
   end;

  for iX := 0 to 200 do
   begin
     MoveMouseTo(WVBrowser1, iX, 0);
     Sleep(5);
   end;
end;

procedure TMainForm.Button1Click(Sender: TObject);
begin
  Click(WVBrowser1, SpinEdit1.Value, SpinEdit2.Value);
end;

procedure TMainForm.Click(WebView: TWVBrowser; TargetX, TargetY: integer);
 var point  : TPoint;
begin
    MoveMouseTo(WebView, TargetX, TargetY);

    point.X:=TargetX;
    point.y:=TargetY;

    WebView.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_DOWN,
                           COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_LEFT_BUTTON,
                           0,
                           point);

    Sleep(RandomRange(50, 150));


    WebView.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_UP,
                           COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_NONE,
                           0,
                           point);
end;

function TMainForm.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
begin
  Result := WVBrowser1.DragEnter(DataObj, grfKeyState, OffsetPointToWebView(pt), LongWord(dwEffect));
end;

function TMainForm.IDropTarget_DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
begin
  Result := WVBrowser1.DragOver(grfKeyState, OffsetPointToWebView(pt), LongWord(dwEffect));
end;

function TMainForm.DragLeave: HRESULT; stdcall;
begin
  Result := WVBrowser1.DragLeave;
end;

function TMainForm.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
begin
  Result := WVBrowser1.Drop(dataObj, grfKeyState, OffsetPointToWebView(pt), LongWord(dwEffect));
end;

function TMainForm.OffsetPointToWebView(aPoint : TPoint) : TPoint;
begin
  Result   := ScreenToClient(aPoint);
  Result.X := Result.X - FWVDirectCompositionHost.Left;
  Result.Y := Result.Y - FWVDirectCompositionHost.Top;
end;

initialization
  GlobalWebView2Loader                := TWVLoader.Create(nil);
  GlobalWebView2Loader.UserDataFolder := ExtractFileDir(Application.ExeName) + '\CustomCache';
  GlobalWebView2Loader.AdditionalBrowserArguments:='--disable-web-security '+
  '--disable-site-isolation-trials '+
  '--allow-file-access-from-files ' +
  '--allow-insecure-localhost';
  GlobalWebView2Loader.StartWebView2;

procedure TMainForm.BtnSolveCaptchaClick(Sender: TObject);
var
 MHTML: string;
 Response: TCaptchaResponse;
 CurrentURL: string;
begin
 try
   FStopwatch := TStopwatch.StartNew;
   CurrentURL := AddressCb.Text;

   Logger.LogCaptchaStart(CurrentURL);
   Logger.Info('Starting CAPTCHA resolution process');

   // Verifica se a API key está configurada
   if not AppConfig.IsAPIKeyValid then
   begin
     Logger.Error('OpenAI API Key not configured');
     ShowMessage('ERRO: Chave API da OpenAI não configurada!' + sLineBreak +
                 'Configure sua chave no arquivo config.ini');
     Exit;
   end;

   // Move o hCaptcha para uma posição conhecida
   Logger.Debug('Moving hCaptcha to known position');
   MoveHcaptcha;

   // Captura o MHTML da página
   Logger.Debug('Attempting to retrieve MHTML content');
   MMHTML.Clear;
   if not WVBrowser1.RetrieveMHTML then
   begin
     Logger.Error('Failed to retrieve MHTML content');
     ShowMessage('Falha ao obter MHTML.');
     Exit;
   end;

   // Aguarda o MHTML ser capturado
   Logger.Debug('Waiting for MHTML capture to complete');
   while MMHTML.Lines.Text = '' do
   begin
     Application.ProcessMessages;
     Sleep(AppConfig.Performance.MessageProcessingDelay);
   end;

   Logger.Info('MHTML captured successfully, size: %d characters', [Length(MMHTML.Lines.Text)]);

   // Analisa com GPT-4o-mini
   Logger.Info('Sending MHTML to OpenAI GPT-4o-mini for analysis');
   Response := SendToGPT4OMini(MMHTML.Lines.Text);

   FStopwatch.Stop;

   if Response.success then
   begin
     Logger.LogCaptchaSuccess(Response.action, Response.confidence, FStopwatch.ElapsedMilliseconds);

     ShowMessage(Format('CAPTCHA analisado com sucesso!' + sLineBreak +
                       'Ação: %s' + sLineBreak +
                       'Confiança: %.2f%%' + sLineBreak +
                       'Tempo: %d ms' + sLineBreak +
                       'Mensagem: %s',
                       [Response.action, Response.confidence * 100,
                        FStopwatch.ElapsedMilliseconds, Response.message]));

     // Executa a ação recomendada
     Logger.Info('Executing recommended action: %s', [Response.action]);
     ExecuteCaptchaAction(Response);
   end
   else
   begin
     Logger.LogCaptchaFailure(Response.message, FStopwatch.ElapsedMilliseconds);
     ShowMessage('Falha ao resolver o CAPTCHA: ' + Response.message);
   end;

 except
   on E: Exception do
   begin
     FStopwatch.Stop;
     Logger.Error('Exception during CAPTCHA resolution: %s', [E.Message]);
     Logger.LogCaptchaFailure(E.Message, FStopwatch.ElapsedMilliseconds);
     ShowMessage('Erro durante resolução do CAPTCHA: ' + E.Message);
   end;
 end;
end;

function TMainForm.SendToGPT4OMini(const MHTML: string): TCaptchaResponse;
var
  CaptchaInfo: string;
  HttpClient: THTTPClient;
  Response: IHTTPResponse;
  JsonRequest, JsonResponse: TJSONObject;
  RequestStream: TStringStream;
  MessagesArray: TJSONArray;
  MessageObj: TJSONObject;
  RequestData: string;
begin
  Result.success := False;
  Result.message := '';
  Result.confidence := 0.0;

  try
    Logger.Debug('Starting GPT-4o-mini analysis');

    // Extrai informações relevantes do MHTML
    CaptchaInfo := ExtractCaptchaInfo(MHTML);

    if CaptchaInfo = '' then
    begin
      Result.message := 'Nenhum CAPTCHA detectado no conteúdo';
      Logger.Warning('No CAPTCHA content detected in MHTML');
      Exit;
    end;

    Logger.Debug('CAPTCHA info extracted, length: %d characters', [Length(CaptchaInfo)]);

    // Prepara a requisição para GPT-4o-mini
    HttpClient := THTTPClient.Create;
    try
      HttpClient.CustomHeaders['Authorization'] := 'Bearer ' + AppConfig.OpenAI.APIKey;
      HttpClient.CustomHeaders['Content-Type'] := 'application/json';

      // Monta o JSON da requisição
      JsonRequest := TJSONObject.Create;
      try
        JsonRequest.AddPair('model', AppConfig.OpenAI.Model);
        JsonRequest.AddPair('max_tokens', TJSONNumber.Create(AppConfig.OpenAI.MaxTokens));
        JsonRequest.AddPair('temperature', TJSONNumber.Create(AppConfig.OpenAI.Temperature));

        MessagesArray := TJSONArray.Create;

        // System message
        MessageObj := TJSONObject.Create;
        MessageObj.AddPair('role', 'system');
        MessageObj.AddPair('content', 'Você é um especialista em análise de CAPTCHAs. Analise o conteúdo HTML fornecido e identifique o tipo de desafio hCAPTCHA presente. Responda APENAS com um JSON no formato: {"action":"click|drag|classify","target":{"x":number,"y":number},"confidence":0.0-1.0,"message":"descrição"}');
        MessagesArray.AddElement(MessageObj);

        // User message
        MessageObj := TJSONObject.Create;
        MessageObj.AddPair('role', 'user');
        MessageObj.AddPair('content', 'Analise este conteúdo de CAPTCHA e determine a ação necessária: ' + CaptchaInfo);
        MessagesArray.AddElement(MessageObj);

        JsonRequest.AddPair('messages', MessagesArray);

        RequestData := JsonRequest.ToString;
        Logger.LogAPIRequest(Copy(RequestData, 1, 500) + '...');

        RequestStream := TStringStream.Create(RequestData, TEncoding.UTF8);
        try
          // Faz a requisição
          Logger.Debug('Sending request to OpenAI API: %s', [AppConfig.OpenAI.APIURL]);
          Response := HttpClient.Post(AppConfig.OpenAI.APIURL, RequestStream);

          Logger.LogAPIResponse(Copy(Response.ContentAsString, 1, 500) + '...', Response.StatusCode);

          if Response.StatusCode = 200 then
          begin
            Logger.Debug('OpenAI API request successful');
            Result := AnalyzeCaptchaWithGPT(Response.ContentAsString);
          end
          else
          begin
            Result.success := False;
            Result.message := 'Erro na API OpenAI: ' + IntToStr(Response.StatusCode) + ' - ' + Response.ContentAsString;
            Logger.Error('OpenAI API error: %d - %s', [Response.StatusCode, Response.ContentAsString]);
          end;
        finally
          RequestStream.Free;
        end;
      finally
        JsonRequest.Free;
      end;
    finally
      HttpClient.Free;
    end;
  except
    on E: Exception do
    begin
      Result.success := False;
      Result.message := 'Erro ao processar requisição GPT: ' + E.Message;
      Logger.Error('Exception in SendToGPT4OMini: %s', [E.Message]);
    end;
  end;
end;

function TMainForm.DetectCaptchaType: string;
var
  Script: string;
  ExecutionResult: string;
begin
  Result := 'objectClick'; // Valor padrão

  Script :=
    '(function() {' + sLineBreak +
    '  try {' + sLineBreak +
    '    var iframes = document.getElementsByTagName("iframe");' + sLineBreak +
    '    var hcaptchaFrame = null;' + sLineBreak +
    '    ' + sLineBreak +
    '    // Encontra o iframe do hCaptcha' + sLineBreak +
    '    for (var i = 0; i < iframes.length; i++) {' + sLineBreak +
    '      if (iframes[i].src.includes("hcaptcha.com")) {' + sLineBreak +
    '        hcaptchaFrame = iframes[i];' + sLineBreak +
    '        break;' + sLineBreak +
    '      }' + sLineBreak +
    '    }' + sLineBreak +
    '    ' + sLineBreak +
    '    if (!hcaptchaFrame) return "unknown";' + sLineBreak +
    '    ' + sLineBreak +
    '    var frameDoc = hcaptchaFrame.contentDocument || hcaptchaFrame.contentWindow.document;' + sLineBreak +
    '    ' + sLineBreak +
    '    // Verifica elementos específicos que indicam o tipo de desafio' + sLineBreak +
    '    if (frameDoc.querySelector(".drag-container") || ' + sLineBreak +
    '        frameDoc.querySelector("[data-drag=true]") || ' + sLineBreak +
    '        frameDoc.querySelector(".draggable")) {' + sLineBreak +
    '      return "objectDrag";' + sLineBreak +
    '    }' + sLineBreak +
    '    ' + sLineBreak +
    '    if (frameDoc.querySelector(".classify-container") || ' + sLineBreak +
    '        frameDoc.querySelector(".classify-prompt") || ' + sLineBreak +
    '        frameDoc.querySelector("[data-classify=true]")) {' + sLineBreak +
    '      return "objectClassify";' + sLineBreak +
    '    }' + sLineBreak +
    '    ' + sLineBreak +
    '    // Se encontrar elementos de imagem clicável, é objectClick' + sLineBreak +
    '    if (frameDoc.querySelector(".task-image") || ' + sLineBreak +
    '        frameDoc.querySelector(".click-area") || ' + sLineBreak +
    '        frameDoc.querySelector("[data-click=true]")) {' + sLineBreak +
    '      return "objectClick";' + sLineBreak +
    '    }' + sLineBreak +
    '    ' + sLineBreak +
    '    return "unknown";' + sLineBreak +
    '  } catch (e) {' + sLineBreak +
    '    console.error("Erro ao detectar tipo de CAPTCHA:", e);' + sLineBreak +
    '    return "unknown";' + sLineBreak +
    '  }' + sLineBreak +
    '})();';

  // Executa o script e aguarda o resultado
  bExecutouScript := False;
  WVBrowser1.ExecuteScript(Script);

  // Aguarda a execução do script
  while not bExecutouScript do
  begin
    Application.ProcessMessages;
    Sleep(100);
  end;

  // Recupera o resultado do script
  if (FLastScriptResult <> '') and (FLastScriptResult <> 'null') then
  begin
    Result := StringReplace(FLastScriptResult, '"', '', [rfReplaceAll]);
  end;

  // Se o resultado for vazio ou "unknown", mantém o valor padrão
  if (Result = '') or (Result = 'unknown') then
    Result := 'objectClick';
end;

function TMainForm.ExtractCaptchaInfo(const HTMLContent: string): string;
var
  StartPos, EndPos: Integer;
  CaptchaSection: string;
begin
  Result := '';

  try
    // Procura por seções relevantes do hCAPTCHA no HTML
    StartPos := Pos('hcaptcha', LowerCase(HTMLContent));
    if StartPos > 0 then
    begin
      // Extrai uma seção do HTML em torno do hCAPTCHA
      StartPos := Max(1, StartPos - 1000);
      EndPos := Min(Length(HTMLContent), StartPos + 5000);
      CaptchaSection := Copy(HTMLContent, StartPos, EndPos - StartPos);

      // Remove dados binários e mantém apenas texto relevante
      CaptchaSection := StringReplace(CaptchaSection, #13#10, ' ', [rfReplaceAll]);
      CaptchaSection := StringReplace(CaptchaSection, #9, ' ', [rfReplaceAll]);

      // Limita o tamanho para não exceder limites da API
      if Length(CaptchaSection) > 3000 then
        CaptchaSection := Copy(CaptchaSection, 1, 3000) + '...';

      Result := CaptchaSection;
    end;
  except
    on E: Exception do
      Result := 'Erro ao extrair informações do CAPTCHA: ' + E.Message;
  end;
end;

function TMainForm.AnalyzeCaptchaWithGPT(const HTMLContent: string): TCaptchaResponse;
var
  JsonResponse: TJSONObject;
  ChoicesArray: TJSONArray;
  MessageObj: TJSONObject;
  ContentStr: string;
  ResultJson: TJSONObject;
  TargetObj: TJSONObject;
begin
  Result.success := False;
  Result.message := '';
  Result.confidence := 0.0;

  try
    JsonResponse := TJSONObject.ParseJSONValue(HTMLContent) as TJSONObject;
    if JsonResponse = nil then
    begin
      Result.message := 'Resposta inválida da API OpenAI';
      Exit;
    end;

    try
      ChoicesArray := JsonResponse.GetValue('choices') as TJSONArray;
      if (ChoicesArray = nil) or (ChoicesArray.Count = 0) then
      begin
        Result.message := 'Nenhuma resposta encontrada';
        Exit;
      end;

      MessageObj := (ChoicesArray.Items[0] as TJSONObject).GetValue('message') as TJSONObject;
      ContentStr := MessageObj.GetValue('content').Value;

      // Parse da resposta JSON do GPT
      ResultJson := TJSONObject.ParseJSONValue(ContentStr) as TJSONObject;
      if ResultJson = nil then
      begin
        Result.message := 'Formato de resposta inválido do GPT';
        Exit;
      end;

      try
        Result.action := ResultJson.GetValue('action').Value;
        Result.confidence := ResultJson.GetValue('confidence').AsType<Double>;
        Result.message := ResultJson.GetValue('message').Value;

        TargetObj := ResultJson.GetValue('target') as TJSONObject;
        if TargetObj <> nil then
        begin
          Result.target.X := TargetObj.GetValue('x').AsType<Integer>;
          Result.target.Y := TargetObj.GetValue('y').AsType<Integer>;
        end;

        Result.success := True;
      finally
        ResultJson.Free;
      end;
    finally
      JsonResponse.Free;
    end;
  except
    on E: Exception do
    begin
      Result.success := False;
      Result.message := 'Erro ao analisar resposta do GPT: ' + E.Message;
    end;
  end;
end;

procedure TMainForm.ExecuteCaptchaAction(const Response: TCaptchaResponse);
var
  StartPoint, EndPoint: TPoint;
  DragSteps: Integer;
  CurrentPoint: TPoint;
  i: Integer;
  Script: string;
  DragDistance: Integer;
begin
  if not Response.success then
  begin
    ShowMessage('Não é possível executar ação: resposta inválida');
    Exit;
  end;

  try
    Logger.Info('Executing CAPTCHA action: %s', [Response.action]);
    Sleep(AppConfig.Performance.MessageProcessingDelay);

    if Response.action = 'click' then
    begin
      Logger.LogMouseAction('Click', Response.target.X, Response.target.Y);
      ShowMessage(Format('Executando clique em (%d, %d)', [Response.target.X, Response.target.Y]));
      Click(WVBrowser1, Response.target.X, Response.target.Y);
    end

    else if Response.action = 'drag' then
    begin
      // Tenta extrair distância da mensagem, ou usa valor padrão
      try
        DragDistance := StrToIntDef(Response.message, 100);
      except
        DragDistance := 100; // Valor padrão
      end;

      // Define os pontos de início e fim do arrasto
      StartPoint := Response.target;
      EndPoint.X := Response.target.X + DragDistance;
      EndPoint.Y := Response.target.Y; // Mantém Y constante para arrasto horizontal

      Logger.LogMouseAction('Drag Start', StartPoint.X, StartPoint.Y);
      Logger.LogMouseAction('Drag End', EndPoint.X, EndPoint.Y);

      ShowMessage(Format('Executando arrasto de (%d, %d) para (%d, %d)',
        [StartPoint.X, StartPoint.Y, EndPoint.X, EndPoint.Y]));

      // Move o mouse para a posição inicial
      MoveMouseTo(WVBrowser1, StartPoint.X, StartPoint.Y);
      Sleep(RandomRange(AppConfig.Performance.DragStartDelayMin, AppConfig.Performance.DragStartDelayMax));

      // Pressiona o botão do mouse
      WVBrowser1.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_DOWN,
                               COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_LEFT_BUTTON,
                               0,
                               StartPoint);

      // Simula o movimento de arrasto em vários passos
      DragSteps := AppConfig.Performance.DragSteps;
      for i := 1 to DragSteps do
      begin
        CurrentPoint.X := StartPoint.X + ((EndPoint.X - StartPoint.X) * i div DragSteps);
        CurrentPoint.Y := StartPoint.Y + ((EndPoint.Y - StartPoint.Y) * i div DragSteps);

        MoveMouseTo(WVBrowser1, CurrentPoint.X, CurrentPoint.Y);
        Sleep(RandomRange(AppConfig.Performance.MouseDelayMin, AppConfig.Performance.MouseDelayMax));
      end;

      // Pequena pausa antes de soltar o botão
      Sleep(RandomRange(AppConfig.Performance.DragStartDelayMin, AppConfig.Performance.DragStartDelayMax));

      // Solta o botão do mouse na posição final
      WVBrowser1.SendMouseInput(COREWEBVIEW2_MOUSE_EVENT_KIND_LEFT_BUTTON_UP,
                               COREWEBVIEW2_MOUSE_EVENT_VIRTUAL_KEYS_NONE,
                               0,
                               EndPoint);
    end

    else if Response.action = 'classify' then
    begin
      Logger.Info('Executing classification action: %s', [Response.message]);
      ShowMessage(Format('Executando classificação: %s', [Response.message]));

      // A mensagem deve conter o índice da categoria (0, 1, 2...)
      Script :=
        '(function() {' + sLineBreak +
        '  try {' + sLineBreak +
        '    var hcaptchaFrame = document.querySelector("iframe[src*=''hcaptcha.com'']");' + sLineBreak +
        '    if (!hcaptchaFrame) return false;' + sLineBreak +
        '    var frameDoc = hcaptchaFrame.contentDocument || hcaptchaFrame.contentWindow.document;' + sLineBreak +
        Format('    var category = frameDoc.querySelector("[data-index=''%s'']");', [Response.message]) + sLineBreak +
        '    if (category) {' + sLineBreak +
        '      category.click();' + sLineBreak +
        '      return true;' + sLineBreak +
        '    }' + sLineBreak +
        '    return false;' + sLineBreak +
        '  } catch (e) {' + sLineBreak +
        '    console.error("Erro ao classificar:", e);' + sLineBreak +
        '    return false;' + sLineBreak +
        '  }' + sLineBreak +
        '})();';

      Logger.Debug('Executing classification script');

      // Executa o script de classificação
      bExecutouScript := False;
      WVBrowser1.ExecuteScript(Script);

      // Aguarda a execução do script
      while not bExecutouScript do
      begin
        Application.ProcessMessages;
        Sleep(AppConfig.Performance.MessageProcessingDelay);
      end;

      Logger.Debug('Classification script completed');
    end
    else
    begin
      Logger.Warning('Unknown action: %s', [Response.action]);
      ShowMessage('Ação não reconhecida: ' + Response.action);
    end;
  except
    on E: Exception do
    begin
      Logger.Error('Exception in ExecuteCaptchaAction: %s', [E.Message]);
      ShowMessage('Erro ao executar ação do CAPTCHA: ' + E.Message);
    end;
  end;
end;

end.
