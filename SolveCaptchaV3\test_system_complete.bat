@echo off
echo ========================================
echo  TESTE COMPLETO DO SISTEMA SolveCaptchaV3
echo ========================================
echo.

set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

echo [INICIANDO] Bateria completa de testes...
echo.

REM ========================================
REM 1. TESTE DE DEPENDÊNCIAS
REM ========================================
echo 1. VERIFICANDO DEPENDÊNCIAS...
set /a TOTAL_TESTS+=1

if exist "dependencies\WebView4Delphi\source" (
    if exist "dependencies\MfPack\MfPack\src" (
        echo   ✅ Dependências OK
        set /a PASSED_TESTS+=1
    ) else (
        echo   ❌ MfPack não encontrado
        set /a FAILED_TESTS+=1
    )
) else (
    echo   ❌ WebView4Delphi não encontrado
    set /a FAILED_TESTS+=1
)

REM ========================================
REM 2. TESTE DE CONFIGURAÇÃO
REM ========================================
echo.
echo 2. VERIFICANDO CONFIGURAÇÃO...
set /a TOTAL_TESTS+=1

if exist "WindowlessBrowser\config.ini" (
    echo   ✅ Arquivo config.ini encontrado
    set /a PASSED_TESTS+=1
) else (
    echo   ❌ Arquivo config.ini não encontrado
    set /a FAILED_TESTS+=1
)

REM ========================================
REM 3. TESTE DE UNIDADES PRINCIPAIS
REM ========================================
echo.
echo 3. VERIFICANDO UNIDADES PRINCIPAIS...
set /a TOTAL_TESTS+=1

set UNITS_OK=1
if not exist "WindowlessBrowser\uConfig.pas" set UNITS_OK=0
if not exist "WindowlessBrowser\uLogger.pas" set UNITS_OK=0
if not exist "WindowlessBrowser\uTests.pas" set UNITS_OK=0
if not exist "WindowlessBrowser\uWindowlessBrowser.pas" set UNITS_OK=0

if %UNITS_OK%==1 (
    echo   ✅ Unidades principais OK
    set /a PASSED_TESTS+=1
) else (
    echo   ❌ Algumas unidades principais estão faltando
    set /a FAILED_TESTS+=1
)

REM ========================================
REM 4. TESTE DE UNIDADES AVANÇADAS
REM ========================================
echo.
echo 4. VERIFICANDO UNIDADES AVANÇADAS...
set /a TOTAL_TESTS+=1

set ADVANCED_OK=1
if not exist "WindowlessBrowser\uStatistics.pas" set ADVANCED_OK=0
if not exist "WindowlessBrowser\uBackupManager.pas" set ADVANCED_OK=0
if not exist "WindowlessBrowser\uProgressIndicator.pas" set ADVANCED_OK=0

if %ADVANCED_OK%==1 (
    echo   ✅ Unidades avançadas OK
    set /a PASSED_TESTS+=1
) else (
    echo   ❌ Algumas unidades avançadas estão faltando
    set /a FAILED_TESTS+=1
)

REM ========================================
REM 5. TESTE DE UNIDADES EMPRESARIAIS
REM ========================================
echo.
echo 5. VERIFICANDO UNIDADES EMPRESARIAIS...
set /a TOTAL_TESTS+=1

set ENTERPRISE_OK=1
if not exist "WindowlessBrowser\uAPIManager.pas" set ENTERPRISE_OK=0
if not exist "WindowlessBrowser\uCacheManager.pas" set ENTERPRISE_OK=0
if not exist "WindowlessBrowser\uPluginManager.pas" set ENTERPRISE_OK=0
if not exist "WindowlessBrowser\uMonitoringSystem.pas" set ENTERPRISE_OK=0
if not exist "WindowlessBrowser\uNotificationSystem.pas" set ENTERPRISE_OK=0

if %ENTERPRISE_OK%==1 (
    echo   ✅ Unidades empresariais OK
    set /a PASSED_TESTS+=1
) else (
    echo   ❌ Algumas unidades empresariais estão faltando
    set /a FAILED_TESTS+=1
)

REM ========================================
REM 6. TESTE DE COMPILAÇÃO
REM ========================================
echo.
echo 6. TESTANDO COMPILAÇÃO...
set /a TOTAL_TESTS+=1

cd WindowlessBrowser
echo   Compilando projeto...

REM Adiciona paths das dependências
set DELPHI_PATHS=-U"..\dependencies\WebView4Delphi\source" -U"..\dependencies\MfPack\MfPack\src"

dcc32 -B -Q %DELPHI_PATHS% WindowlessBrowser.dpr >nul 2>&1
if errorlevel 1 (
    echo   ❌ Falha na compilação
    set /a FAILED_TESTS+=1
) else (
    echo   ✅ Compilação bem-sucedida
    set /a PASSED_TESTS+=1
)

REM ========================================
REM 7. TESTE DE EXECUTÁVEL
REM ========================================
echo.
echo 7. VERIFICANDO EXECUTÁVEL...
set /a TOTAL_TESTS+=1

if exist "WindowlessBrowser.exe" (
    echo   ✅ Executável gerado com sucesso
    for %%A in (WindowlessBrowser.exe) do (
        echo   📊 Tamanho: %%~zA bytes
    )
    set /a PASSED_TESTS+=1
) else (
    echo   ❌ Executável não encontrado
    set /a FAILED_TESTS+=1
)

cd ..

REM ========================================
REM RELATÓRIO FINAL
REM ========================================
echo.
echo ========================================
echo  RELATÓRIO FINAL DOS TESTES
echo ========================================
echo.
echo Total de Testes: %TOTAL_TESTS%
echo Testes Aprovados: %PASSED_TESTS%
echo Testes Falharam: %FAILED_TESTS%

set /a SUCCESS_RATE=(%PASSED_TESTS% * 100) / %TOTAL_TESTS%
echo Taxa de Sucesso: %SUCCESS_RATE%%%

echo.
if %FAILED_TESTS%==0 (
    echo 🎉 PARABÉNS! TODOS OS TESTES PASSARAM!
    echo ✅ O sistema está TOTALMENTE FUNCIONAL
    echo.
    echo 📋 Próximos passos:
    echo   1. Configure sua chave OpenAI no config.ini
    echo   2. Execute o programa: WindowlessBrowser.exe
    echo   3. Teste com uma página que contenha hCAPTCHA
    echo   4. Verifique os logs gerados
) else (
    echo ❌ ALGUNS TESTES FALHARAM
    echo ⚠️  Verifique os problemas acima antes de usar o sistema
)

echo.
echo ========================================
echo  TESTE COMPLETO FINALIZADO
echo ========================================
pause
