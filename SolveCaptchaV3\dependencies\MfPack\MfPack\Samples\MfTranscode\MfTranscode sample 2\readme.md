# MfTranscode example 2
Version: X 3.1.7

NOTES: 
 - This release is updated for compiler version 17 up to 35.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 7 or later.
 - Minimum supported MfPack version: 3.1.6

Description:
Demonstrates using the transcode API to transcode a source file (audio or video) to
a different format (audio or video) supported by Media Foundation.
The sample audio can be configured in detail, dependent of the audio codec supported by Media Foundation.
The video format can be configured within the margins of the resolution format.

Supported, but not limited, formats in this sample are:

Audio
  Waveform Audio File Format (wav)
  MPEG Audio Layer III (mp3)
  Free Lossless Audio Codec (flac)
  MPEG-4 Audio (m4a)
  Windows Media Audio (wma)

Video
  Audio Video Interleave (avi)
  MPEG-4 Video with AAC Audio (mp4)
  MPEG-4 Video with Dolby AC-3 Audio (mp4)
  Windows Media Video (wmv)


Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 24/01/2020
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.