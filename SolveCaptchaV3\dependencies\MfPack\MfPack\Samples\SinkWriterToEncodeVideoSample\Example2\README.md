# SinkWriterToEncodeVideoSample - Part 2

Version: X 3.1.7

Description:
  This sample demonstrates how to use the SinkWriter to encode a video file from
  one or more bitmap files.
  Note that the bitmaps are loaded and processed in memory. 
  Supported output formats are: MP4, AVI and WMF.

NOTES:
 - This release is updated for compiler version 17 up to 34.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 10 or later.
 - Minimum supported MfPack version: 3.1.5

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 25-11-2022
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.




