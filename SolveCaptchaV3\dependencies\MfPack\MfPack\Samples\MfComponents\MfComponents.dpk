package MfComponents;

{$R *.res}
{$IFDEF IMPLICITBUILDING This IFDEF should not be used by users}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMP<PERSON><PERSON>DDA<PERSON> ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION OFF}
{$OVERFLOWCHECKS ON}
{$RANGECHECKS ON}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES ON}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DEFINE DEBUG}
{$ENDIF IMPLICITBUILDING}
{$DESCRIPTION 'MfComponents for Delphi'}
{$IMPLICITBUILD ON}

requires
  rtl,
  MfPackX317;

contains
  MfAudioEndPoint in 'MfAudioEndPoint.pas',
  Que<PERSON><PERSON>imer in 'QueueTimer.pas',
  ThreadedQueueTimer in 'ThreadedQueueTimer.pas',
  MfPeakMeterEx in 'MfPeakMeterEx.pas',
  MfPeakMeter in 'MfPeakMeter.pas';

end.

