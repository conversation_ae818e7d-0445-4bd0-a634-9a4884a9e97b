unit uConfig;

interface

uses
  System.SysUtils, System.IniFiles, System.Classes, Vcl.Forms;

type
  TOpenAIConfig = record
    APIKey: string;
    Model: string;
    Temperature: Double;
    MaxTokens: Integer;
    APIURL: string;
  end;

  TCaptchaConfig = record
    LoadTimeout: Integer;
    ScriptTimeout: Integer;
    DefaultX: Integer;
    DefaultY: Integer;
    DefaultWidth: Integer;
    DefaultHeight: Integer;
  end;

  TWebView2Config = record
    AdditionalArgs: string;
    CacheDirectory: string;
    TargetVersion: string;
  end;

  TLoggingConfig = record
    EnableLogging: Boolean;
    LogLevel: string;
    LogFile: string;
  end;

  TPerformanceConfig = record
    MouseDelayMin: Integer;
    MouseDelayMax: Integer;
    DragStartDelayMin: Integer;
    DragStartDelayMax: Integer;
    DragSteps: Integer;
    MessageProcessingDelay: Integer;
  end;

  TAppConfig = class
  private
    FConfigFile: string;
    FOpenAI: TOpenAIConfig;
    FCaptcha: TCaptchaConfig;
    FWebView2: TWebView2Config;
    FLogging: TLoggingConfig;
    FPerformance: TPerformanceConfig;

    procedure LoadDefaults;
    procedure ValidateConfig;
    function GetConfigFilePath: string;

  public
    constructor Create;
    destructor Destroy; override;

    procedure LoadFromFile(const FileName: string = '');
    procedure SaveToFile(const FileName: string = '');

    // Propriedades de acesso
    property OpenAI: TOpenAIConfig read FOpenAI write FOpenAI;
    property Captcha: TCaptchaConfig read FCaptcha write FCaptcha;
    property WebView2: TWebView2Config read FWebView2 write FWebView2;
    property Logging: TLoggingConfig read FLogging write FLogging;
    property Performance: TPerformanceConfig read FPerformance write FPerformance;

    // Métodos utilitários
    function IsAPIKeyValid: Boolean;
    function GetLogFilePath: string;
    function GetCacheDirectoryPath: string;
    function GetConfigFilePathPublic: string;
    procedure LogConfig;
  end;

var
  AppConfig: TAppConfig;

implementation

uses
  System.IOUtils;

{ TAppConfig }

constructor TAppConfig.Create;
begin
  inherited Create;
  LoadDefaults;
  FConfigFile := GetConfigFilePath;

  // Tenta carregar configurações do arquivo
  if FileExists(FConfigFile) then
    LoadFromFile(FConfigFile)
  else
  begin
    // Se não existe config.ini, tenta carregar do exemplo
    if FileExists(ChangeFileExt(FConfigFile, '_example.ini')) then
      LoadFromFile(ChangeFileExt(FConfigFile, '_example.ini'));
  end;

  ValidateConfig;
end;

destructor TAppConfig.Destroy;
begin
  inherited Destroy;
end;

procedure TAppConfig.LoadDefaults;
begin
  // Configurações padrão OpenAI
  FOpenAI.APIKey := 'sk-your-openai-api-key-here';
  FOpenAI.Model := 'gpt-4o-mini';
  FOpenAI.Temperature := 0.1;
  FOpenAI.MaxTokens := 500;
  FOpenAI.APIURL := 'https://api.openai.com/v1/chat/completions';

  // Configurações padrão CAPTCHA
  FCaptcha.LoadTimeout := 30;
  FCaptcha.ScriptTimeout := 10;
  FCaptcha.DefaultX := 230;
  FCaptcha.DefaultY := 378;
  FCaptcha.DefaultWidth := 303;
  FCaptcha.DefaultHeight := 78;

  // Configurações padrão WebView2
  FWebView2.AdditionalArgs := '--disable-web-security --disable-site-isolation-trials --allow-file-access-from-files --allow-insecure-localhost';
  FWebView2.CacheDirectory := 'CustomCache';
  FWebView2.TargetVersion := '95.0.1020.44';

  // Configurações padrão Logging
  FLogging.EnableLogging := True;
  FLogging.LogLevel := 'INFO';
  FLogging.LogFile := 'captcha_solver.log';

  // Configurações padrão Performance
  FPerformance.MouseDelayMin := 20;
  FPerformance.MouseDelayMax := 50;
  FPerformance.DragStartDelayMin := 100;
  FPerformance.DragStartDelayMax := 200;
  FPerformance.DragSteps := 10;
  FPerformance.MessageProcessingDelay := 100;
end;

function TAppConfig.GetConfigFilePath: string;
begin
  Result := TPath.Combine(ExtractFilePath(Application.ExeName), 'config.ini');
end;

procedure TAppConfig.LoadFromFile(const FileName: string);
var
  IniFile: TIniFile;
  ConfigPath: string;
begin
  if FileName = '' then
    ConfigPath := FConfigFile
  else
    ConfigPath := FileName;

  if not FileExists(ConfigPath) then
    Exit;

  IniFile := TIniFile.Create(ConfigPath);
  try
    // Seção OpenAI
    FOpenAI.APIKey := IniFile.ReadString('OpenAI', 'api_key', FOpenAI.APIKey);
    FOpenAI.Model := IniFile.ReadString('OpenAI', 'model', FOpenAI.Model);
    FOpenAI.Temperature := IniFile.ReadFloat('OpenAI', 'temperature', FOpenAI.Temperature);
    FOpenAI.MaxTokens := IniFile.ReadInteger('OpenAI', 'max_tokens', FOpenAI.MaxTokens);
    FOpenAI.APIURL := IniFile.ReadString('OpenAI', 'api_url', FOpenAI.APIURL);

    // Seção CAPTCHA
    FCaptcha.LoadTimeout := IniFile.ReadInteger('CAPTCHA', 'load_timeout', FCaptcha.LoadTimeout);
    FCaptcha.ScriptTimeout := IniFile.ReadInteger('CAPTCHA', 'script_timeout', FCaptcha.ScriptTimeout);
    FCaptcha.DefaultX := IniFile.ReadInteger('CAPTCHA', 'default_x', FCaptcha.DefaultX);
    FCaptcha.DefaultY := IniFile.ReadInteger('CAPTCHA', 'default_y', FCaptcha.DefaultY);
    FCaptcha.DefaultWidth := IniFile.ReadInteger('CAPTCHA', 'default_width', FCaptcha.DefaultWidth);
    FCaptcha.DefaultHeight := IniFile.ReadInteger('CAPTCHA', 'default_height', FCaptcha.DefaultHeight);

    // Seção WebView2
    FWebView2.AdditionalArgs := IniFile.ReadString('WebView2', 'additional_args', FWebView2.AdditionalArgs);
    FWebView2.CacheDirectory := IniFile.ReadString('WebView2', 'cache_directory', FWebView2.CacheDirectory);
    FWebView2.TargetVersion := IniFile.ReadString('WebView2', 'target_version', FWebView2.TargetVersion);

    // Seção Logging
    FLogging.EnableLogging := IniFile.ReadBool('Logging', 'enable_logging', FLogging.EnableLogging);
    FLogging.LogLevel := IniFile.ReadString('Logging', 'log_level', FLogging.LogLevel);
    FLogging.LogFile := IniFile.ReadString('Logging', 'log_file', FLogging.LogFile);

    // Seção Performance
    FPerformance.MouseDelayMin := IniFile.ReadInteger('Performance', 'mouse_delay_min', FPerformance.MouseDelayMin);
    FPerformance.MouseDelayMax := IniFile.ReadInteger('Performance', 'mouse_delay_max', FPerformance.MouseDelayMax);
    FPerformance.DragStartDelayMin := IniFile.ReadInteger('Performance', 'drag_start_delay_min', FPerformance.DragStartDelayMin);
    FPerformance.DragStartDelayMax := IniFile.ReadInteger('Performance', 'drag_start_delay_max', FPerformance.DragStartDelayMax);
    FPerformance.DragSteps := IniFile.ReadInteger('Performance', 'drag_steps', FPerformance.DragSteps);
    FPerformance.MessageProcessingDelay := IniFile.ReadInteger('Performance', 'message_processing_delay', FPerformance.MessageProcessingDelay);

  finally
    IniFile.Free;
  end;
end;

procedure TAppConfig.SaveToFile(const FileName: string);
var
  IniFile: TIniFile;
  ConfigPath: string;
begin
  if FileName = '' then
    ConfigPath := FConfigFile
  else
    ConfigPath := FileName;

  IniFile := TIniFile.Create(ConfigPath);
  try
    // Seção OpenAI
    IniFile.WriteString('OpenAI', 'api_key', FOpenAI.APIKey);
    IniFile.WriteString('OpenAI', 'model', FOpenAI.Model);
    IniFile.WriteFloat('OpenAI', 'temperature', FOpenAI.Temperature);
    IniFile.WriteInteger('OpenAI', 'max_tokens', FOpenAI.MaxTokens);
    IniFile.WriteString('OpenAI', 'api_url', FOpenAI.APIURL);

    // Seção CAPTCHA
    IniFile.WriteInteger('CAPTCHA', 'load_timeout', FCaptcha.LoadTimeout);
    IniFile.WriteInteger('CAPTCHA', 'script_timeout', FCaptcha.ScriptTimeout);
    IniFile.WriteInteger('CAPTCHA', 'default_x', FCaptcha.DefaultX);
    IniFile.WriteInteger('CAPTCHA', 'default_y', FCaptcha.DefaultY);
    IniFile.WriteInteger('CAPTCHA', 'default_width', FCaptcha.DefaultWidth);
    IniFile.WriteInteger('CAPTCHA', 'default_height', FCaptcha.DefaultHeight);

    // Seção WebView2
    IniFile.WriteString('WebView2', 'additional_args', FWebView2.AdditionalArgs);
    IniFile.WriteString('WebView2', 'cache_directory', FWebView2.CacheDirectory);
    IniFile.WriteString('WebView2', 'target_version', FWebView2.TargetVersion);

    // Seção Logging
    IniFile.WriteBool('Logging', 'enable_logging', FLogging.EnableLogging);
    IniFile.WriteString('Logging', 'log_level', FLogging.LogLevel);
    IniFile.WriteString('Logging', 'log_file', FLogging.LogFile);

    // Seção Performance
    IniFile.WriteInteger('Performance', 'mouse_delay_min', FPerformance.MouseDelayMin);
    IniFile.WriteInteger('Performance', 'mouse_delay_max', FPerformance.MouseDelayMax);
    IniFile.WriteInteger('Performance', 'drag_start_delay_min', FPerformance.DragStartDelayMin);
    IniFile.WriteInteger('Performance', 'drag_start_delay_max', FPerformance.DragStartDelayMax);
    IniFile.WriteInteger('Performance', 'drag_steps', FPerformance.DragSteps);
    IniFile.WriteInteger('Performance', 'message_processing_delay', FPerformance.MessageProcessingDelay);

  finally
    IniFile.Free;
  end;
end;

procedure TAppConfig.ValidateConfig;
begin
  // Validações básicas
  if FOpenAI.Temperature < 0 then FOpenAI.Temperature := 0;
  if FOpenAI.Temperature > 1 then FOpenAI.Temperature := 1;
  if FOpenAI.MaxTokens < 1 then FOpenAI.MaxTokens := 500;
  if FCaptcha.LoadTimeout < 1 then FCaptcha.LoadTimeout := 30;
  if FCaptcha.ScriptTimeout < 1 then FCaptcha.ScriptTimeout := 10;
  if FPerformance.MouseDelayMin < 0 then FPerformance.MouseDelayMin := 20;
  if FPerformance.MouseDelayMax < FPerformance.MouseDelayMin then FPerformance.MouseDelayMax := FPerformance.MouseDelayMin + 30;
  if FPerformance.DragSteps < 1 then FPerformance.DragSteps := 10;
end;

function TAppConfig.IsAPIKeyValid: Boolean;
begin
  Result := (FOpenAI.APIKey <> '') and
            (FOpenAI.APIKey <> 'sk-your-openai-api-key-here') and
            (Pos('sk-', FOpenAI.APIKey) = 1);
end;

function TAppConfig.GetLogFilePath: string;
begin
  if TPath.IsPathRooted(FLogging.LogFile) then
    Result := FLogging.LogFile
  else
    Result := TPath.Combine(ExtractFilePath(Application.ExeName), FLogging.LogFile);
end;

function TAppConfig.GetCacheDirectoryPath: string;
begin
  if TPath.IsPathRooted(FWebView2.CacheDirectory) then
    Result := FWebView2.CacheDirectory
  else
    Result := TPath.Combine(ExtractFilePath(Application.ExeName), FWebView2.CacheDirectory);
end;

function TAppConfig.GetConfigFilePathPublic: string;
begin
  Result := FConfigFile;
end;

procedure TAppConfig.LogConfig;
begin
  // Esta função pode ser usada para logar as configurações atuais
  // Implementação será adicionada quando o sistema de logging estiver pronto
end;

initialization
  AppConfig := TAppConfig.Create;

finalization
  AppConfig.Free;

end.
