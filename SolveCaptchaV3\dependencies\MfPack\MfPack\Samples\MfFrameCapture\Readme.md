# MfFrameCapture

Version: X 3.1.7

Description:

  Demonstrates how to capture an image (synchronous or A-synchronous) from a mediafile using the Source Reader.

NOTES: 
 - This release is updated for compiler version 17 up to 34.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 8 or later.
 - Minimum supported MfPack version: 3.1.5

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 05-07-2020
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.