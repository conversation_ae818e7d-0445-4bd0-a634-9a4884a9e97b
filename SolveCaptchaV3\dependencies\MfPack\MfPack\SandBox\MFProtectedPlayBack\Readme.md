# MFProtectedPlayBack Sample
Version: under construction

Description:

  A DRM enabled player component (TControl) of audio and video using Media Foundation.
  This sample is a Delphi (FireMonkey) version of the [ProtectedPlayback Sample](https://docs.microsoft.com/en-us/windows/win32/medfound/protectedplayback-sample) included in the Media Foundation SDK. 

NOTES: 
 - This release is updated for compiler version 17 up to 35.
 - SDK version 10.0.22000.0 (Win 11)
 - Requires Windows 10 or later.
 - Minimum supported MfPack version: 3.1.1

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 01-02-2022
Final release date: unknown

Copyright © FactoryX. All rights reserved.