package MfPackX317;

{$R *.res}
{$IFDEF IMPLICITBUILDING This IFDEF should not be used by users}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPOR<PERSON><PERSON><PERSON><PERSON> ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION OFF}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES ON}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DEFINE DEBUG}
{$ENDIF IMPLICITBUILDING}
{$IMPLICITBUILD ON}

requires
  rtl,
  vcl;

contains
  System.Services.AtlThunk in '..\src\System.Services.AtlThunk.pas',
  System.Services.Avrt in '..\src\System.Services.Avrt.pas',
  System.Services.Dbt in '..\src\System.Services.Dbt.pas',
  WinApi.ActiveX.OaIdl in '..\src\WinApi.ActiveX.OaIdl.pas',
  WinApi.ActiveX.ObjBase in '..\src\WinApi.ActiveX.ObjBase.pas',
  WinApi.ActiveX.ObjIdl in '..\src\WinApi.ActiveX.ObjIdl.pas',
  WinApi.ActiveX.ObjIdlbase in '..\src\WinApi.ActiveX.ObjIdlbase.pas',
  WinApi.ActiveX.OCIdl in '..\src\WinApi.ActiveX.OCIdl.pas',
  WinApi.ActiveX.OleIdl in '..\src\WinApi.ActiveX.OleIdl.pas',
  WinApi.ActiveX.PropIdl in '..\src\WinApi.ActiveX.PropIdl.pas',
  WinApi.ActiveX.PropKeyDef in '..\src\WinApi.ActiveX.PropKeyDef.pas',
  WinApi.ActiveX.PropSys in '..\src\WinApi.ActiveX.PropSys.pas',
  WinApi.ActiveX.PropVarUtil in '..\src\WinApi.ActiveX.PropVarUtil.pas',
  WinApi.ActiveX.RoBuffer in '..\src\WinApi.ActiveX.RoBuffer.pas',
  WinApi.AmVideo in '..\src\WinApi.AmVideo.pas',
  WinApi.ComBaseApi in '..\src\WinApi.ComBaseApi.pas',
  WinApi.Coml2Api in '..\src\WinApi.Coml2Api.pas',
  WinApi.CoreAudioApi.AudEvCod in '..\src\WinApi.CoreAudioApi.AudEvCod.pas',
  WinApi.CoreAudioApi.AudioAPOTypes in '..\src\WinApi.CoreAudioApi.AudioAPOTypes.pas',
  WinApi.CoreAudioApi.AudioClient in '..\src\WinApi.CoreAudioApi.AudioClient.pas',
  WinApi.CoreAudioApi.AudioClientActivationParams in '..\src\WinApi.CoreAudioApi.AudioClientActivationParams.pas',
  WinApi.CoreAudioApi.AudioEndpoints in '..\src\WinApi.CoreAudioApi.AudioEndpoints.pas',
  WinApi.CoreAudioApi.AudioengineBaseApo in '..\src\WinApi.CoreAudioApi.AudioengineBaseApo.pas',
  WinApi.CoreAudioApi.AudioEngineEndpoint in '..\src\WinApi.CoreAudioApi.AudioEngineEndpoint.pas',
  WinApi.CoreAudioApi.AudioEngineExtensionApo in '..\src\WinApi.CoreAudioApi.AudioEngineExtensionApo.pas',
  WinApi.CoreAudioApi.AudioMediaType in '..\src\WinApi.CoreAudioApi.AudioMediaType.pas',
  WinApi.CoreAudioApi.AudioPolicy in '..\src\WinApi.CoreAudioApi.AudioPolicy.pas',
  WinApi.CoreAudioApi.AudioSessionTypes in '..\src\WinApi.CoreAudioApi.AudioSessionTypes.pas',
  WinApi.CoreAudioApi.AudioStateMonitorApi in '..\src\WinApi.CoreAudioApi.AudioStateMonitorApi.pas',
  WinApi.CoreAudioApi.DeviceTopology in '..\src\WinApi.CoreAudioApi.DeviceTopology.pas',
  WinApi.CoreAudioApi.DevTopoUtils in '..\src\WinApi.CoreAudioApi.DevTopoUtils.pas',
  WinApi.CoreAudioApi.Endpointvolume in '..\src\WinApi.CoreAudioApi.Endpointvolume.pas',
  WinApi.CoreAudioApi.FunctionDiscoveryKeys_devpkey in '..\src\WinApi.CoreAudioApi.FunctionDiscoveryKeys_devpkey.pas',
  WinApi.CoreAudioApi.MMDevApiUtils in '..\src\WinApi.CoreAudioApi.MMDevApiUtils.pas',
  WinApi.CoreAudioApi.MMDeviceApi in '..\src\WinApi.CoreAudioApi.MMDeviceApi.pas',
  WinApi.CoreAudioApi.SpatialAudioClient in '..\src\WinApi.CoreAudioApi.SpatialAudioClient.pas',
  WinApi.CoreAudioApi.SpatialAudioHrtf in '..\src\WinApi.CoreAudioApi.SpatialAudioHrtf.pas',
  WinApi.CoreAudioApi.SpatialAudioMetadata in '..\src\WinApi.CoreAudioApi.SpatialAudioMetadata.pas',
  WinApi.CoreAudioApi.AudioProfileUtils in '..\src\WinApi.CoreAudioApi.AudioProfileUtils.pas',
  WinApi.DevpKey in '..\src\WinApi.DevpKey.pas',
  WinApi.DevPropDef in '..\src\WinApi.DevPropDef.pas',
  WinApi.DirectX.D2D1 in '..\src\WinApi.DirectX.D2D1.pas',
  WinApi.DirectX.D2D1_1 in '..\src\WinApi.DirectX.D2D1_1.pas',
  WinApi.DirectX.D2D1_1Helper in '..\src\WinApi.DirectX.D2D1_1Helper.pas',
  WinApi.DirectX.D2D1_2 in '..\src\WinApi.DirectX.D2D1_2.pas',
  WinApi.DirectX.D2D1_2Helper in '..\src\WinApi.DirectX.D2D1_2Helper.pas',
  WinApi.DirectX.D2D1_3 in '..\src\WinApi.DirectX.D2D1_3.pas',
  WinApi.DirectX.D2D1_3Helper in '..\src\WinApi.DirectX.D2D1_3Helper.pas',
  WinApi.DirectX.D2D1EffectAuthor in '..\src\WinApi.DirectX.D2D1EffectAuthor.pas',
  WinApi.DirectX.D2D1EffectAuthor_1 in '..\src\WinApi.DirectX.D2D1EffectAuthor_1.pas',
  WinApi.DirectX.D2D1Effects in '..\src\WinApi.DirectX.D2D1Effects.pas',
  WinApi.DirectX.D2D1Effects_1 in '..\src\WinApi.DirectX.D2D1Effects_1.pas',
  WinApi.DirectX.D2D1Effects_2 in '..\src\WinApi.DirectX.D2D1Effects_2.pas',
  WinApi.DirectX.D2D1Helper in '..\src\WinApi.DirectX.D2D1Helper.pas',
  WinApi.DirectX.D2D1Svg in '..\src\WinApi.DirectX.D2D1Svg.pas',
  WinApi.DirectX.D2DBaseTypes in '..\src\WinApi.DirectX.D2DBaseTypes.pas',
  WinApi.DirectX.D2DErr in '..\src\WinApi.DirectX.D2DErr.pas',
  WinApi.DirectX.D3D11 in '..\src\WinApi.DirectX.D3D11.pas',
  WinApi.DirectX.D3D11_1 in '..\src\WinApi.DirectX.D3D11_1.pas',
  WinApi.DirectX.D3D11_2 in '..\src\WinApi.DirectX.D3D11_2.pas',
  WinApi.DirectX.D3D11_4 in '..\src\WinApi.DirectX.D3D11_4.pas',
  WinApi.DirectX.D3D11on12 in '..\src\WinApi.DirectX.D3D11on12.pas',
  WinApi.DirectX.D3D11Shader in '..\src\WinApi.DirectX.D3D11Shader.pas',
  WinApi.DirectX.D3D12 in '..\src\WinApi.DirectX.D3D12.pas',
  WinApi.DirectX.D3D12Compatibility in '..\src\WinApi.DirectX.D3D12Compatibility.pas',
  WinApi.DirectX.D3D12SDKLayers in '..\src\WinApi.DirectX.D3D12SDKLayers.pas',
  WinApi.DirectX.D3D12Shader in '..\src\WinApi.DirectX.D3D12Shader.pas',
  WinApi.DirectX.D3D12Video in '..\src\WinApi.DirectX.D3D12Video.pas',
  WinApi.DirectX.D3D9 in '..\src\WinApi.DirectX.D3D9.pas',
  WinApi.DirectX.D3D9Caps in '..\src\WinApi.DirectX.D3D9Caps.pas',
  WinApi.DirectX.D3D9Types in '..\src\WinApi.DirectX.D3D9Types.pas',
  WinApi.DirectX.D3DCommon in '..\src\WinApi.DirectX.D3DCommon.pas',
  WinApi.DirectX.DCommon in '..\src\WinApi.DirectX.DCommon.pas',
  WinApi.DirectX.DComp in '..\src\WinApi.DirectX.DComp.pas',
  WinApi.DirectX.DCompAnimation in '..\src\WinApi.DirectX.DCompAnimation.pas',
  WinApi.DirectX.DCompTypes in '..\src\WinApi.DirectX.DCompTypes.pas',
  WinApi.DirectX.DocumentTarget in '..\src\WinApi.DirectX.DocumentTarget.pas',
  WinApi.DirectX.DWrite in '..\src\WinApi.DirectX.DWrite.pas',
  WinApi.DirectX.DWrite_1 in '..\src\WinApi.DirectX.DWrite_1.pas',
  WinApi.DirectX.DWrite_2 in '..\src\WinApi.DirectX.DWrite_2.pas',
  WinApi.DirectX.DWrite_3 in '..\src\WinApi.DirectX.DWrite_3.pas',
  WinApi.DirectX.DXGI in '..\src\WinApi.DirectX.DXGI.pas',
  WinApi.DirectX.DXGI1_2 in '..\src\WinApi.DirectX.DXGI1_2.pas',
  WinApi.DirectX.DXGI1_3 in '..\src\WinApi.DirectX.DXGI1_3.pas',
  WinApi.DirectX.DXGI1_4 in '..\src\WinApi.DirectX.DXGI1_4.pas',
  WinApi.DirectX.DXGI1_5 in '..\src\WinApi.DirectX.DXGI1_5.pas',
  WinApi.DirectX.DXGI1_6 in '..\src\WinApi.DirectX.DXGI1_6.pas',
  WinApi.DirectX.DXGICommon in '..\src\WinApi.DirectX.DXGICommon.pas',
  WinApi.DirectX.DXGIFormat in '..\src\WinApi.DirectX.DXGIFormat.pas',
  WinApi.DirectX.DXGIMessages in '..\src\WinApi.DirectX.DXGIMessages.pas',
  WinApi.DirectX.DXGIType in '..\src\WinApi.DirectX.DXGIType.pas',
  WinApi.DirectX.DXVA in '..\src\WinApi.DirectX.DXVA.pas',
  WinApi.DirectX.DXVA2Api in '..\src\WinApi.DirectX.DXVA2Api.pas',
  WinApi.DirectX.DXVA2SWDev in '..\src\WinApi.DirectX.DXVA2SWDev.pas',
  WinApi.DirectX.DXVA2Trace in '..\src\WinApi.DirectX.DXVA2Trace.pas',
  WinApi.DirectX.DXVA9Typ in '..\src\WinApi.DirectX.DXVA9Typ.pas',
  WinApi.DirectX.DXVAHd in '..\src\WinApi.DirectX.DXVAHd.pas',
  WinApi.DirectX.HolographicSpaceInterop in '..\src\WinApi.DirectX.HolographicSpaceInterop.pas',
  WinApi.DirectX.XAudio2.HrtfApoApi in '..\src\WinApi.DirectX.XAudio2.HrtfApoApi.pas',
  WinApi.DirectX.XAudio2.X3DAudio in '..\src\WinApi.DirectX.XAudio2.X3DAudio.pas',
  WinApi.DirectX.XAudio2.XAPO in '..\src\WinApi.DirectX.XAudio2.XAPO.pas',
  WinApi.DirectX.XAudio2.XAPOBase in '..\src\WinApi.DirectX.XAudio2.XAPOBase.pas',
  WinApi.DirectX.XAudio2.XAPOFx in '..\src\WinApi.DirectX.XAudio2.XAPOFx.pas',
  WinApi.DirectX.XAudio2.XAudio2 in '..\src\WinApi.DirectX.XAudio2.XAudio2.pas',
  WinApi.DirectX.XAudio2.XAudio2Fx in '..\src\WinApi.DirectX.XAudio2.XAudio2Fx.pas',
  WinApi.DirectX.XInput in '..\src\WinApi.DirectX.XInput.pas',
  WinApi.DirectX.D3D11_3 in '..\src\WinApi.DirectX.D3D11_3.pas',
  WinApi.DvdMedia in '..\src\WinApi.DvdMedia.pas',
  WinApi.Evntcons in '..\src\WinApi.Evntcons.pas',
  WinApi.Evntprov in '..\src\WinApi.Evntprov.pas',
  WinApi.Evntrace in '..\src\WinApi.Evntrace.pas',
  WinApi.InitGuid in '..\src\WinApi.InitGuid.pas',
  WinApi.Inspectable in '..\src\WinApi.Inspectable.pas',
  WinApi.Ks in '..\src\WinApi.Ks.pas',
  WinApi.KsMedia in '..\src\WinApi.KsMedia.pas',
  WinApi.KsProxy in '..\src\WinApi.KsProxy.pas',
  WinApi.KsUuIds in '..\src\WinApi.KsUuIds.pas',
  WinApi.MediaFoundationApi.CameraUIControl in '..\src\WinApi.MediaFoundationApi.CameraUIControl.pas',
  WinApi.MediaFoundationApi.CodecApi in '..\src\WinApi.MediaFoundationApi.CodecApi.pas',
  WinApi.MediaFoundationApi.Evr in '..\src\WinApi.MediaFoundationApi.Evr.pas',
  WinApi.MediaFoundationApi.Evr9 in '..\src\WinApi.MediaFoundationApi.Evr9.pas',
  WinApi.MediaFoundationApi.ICodecApi in '..\src\WinApi.MediaFoundationApi.ICodecApi.pas',
  WinApi.MediaFoundationApi.KsOpmApi in '..\src\WinApi.MediaFoundationApi.KsOpmApi.pas',
  WinApi.MediaFoundationApi.MfApi in '..\src\WinApi.MediaFoundationApi.MfApi.pas',
  WinApi.MediaFoundationApi.MfCaptureEngine in '..\src\WinApi.MediaFoundationApi.MfCaptureEngine.pas',
  WinApi.MediaFoundationApi.MfContentDecryptionModule in '..\src\WinApi.MediaFoundationApi.MfContentDecryptionModule.pas',
  WinApi.MediaFoundationApi.MfD3D12 in '..\src\WinApi.MediaFoundationApi.MfD3D12.pas',
  WinApi.MediaFoundationApi.MfError in '..\src\WinApi.MediaFoundationApi.MfError.pas',
  WinApi.MediaFoundationApi.MfIdl in '..\src\WinApi.MediaFoundationApi.MfIdl.pas',
  WinApi.MediaFoundationApi.MfMediaCapture in '..\src\WinApi.MediaFoundationApi.MfMediaCapture.pas',
  WinApi.MediaFoundationApi.MfMediaEngine in '..\src\WinApi.MediaFoundationApi.MfMediaEngine.pas',
  WinApi.MediaFoundationApi.MfMediaTypeDebug in '..\src\WinApi.MediaFoundationApi.MfMediaTypeDebug.pas',
  WinApi.MediaFoundationApi.MfMetLib in '..\src\WinApi.MediaFoundationApi.MfMetLib.pas',
  WinApi.MediaFoundationApi.MfMp2Dlna in '..\src\WinApi.MediaFoundationApi.MfMp2Dlna.pas',
  WinApi.MediaFoundationApi.MfObjects in '..\src\WinApi.MediaFoundationApi.MfObjects.pas',
  WinApi.MediaFoundationApi.MfPlay in '..\src\WinApi.MediaFoundationApi.MfPlay.pas',
  WinApi.MediaFoundationApi.MfReadWrite in '..\src\WinApi.MediaFoundationApi.MfReadWrite.pas',
  WinApi.MediaFoundationApi.MfSharingEngine in '..\src\WinApi.MediaFoundationApi.MfSharingEngine.pas',
  WinApi.MediaFoundationApi.MfSpatialAudio in '..\src\WinApi.MediaFoundationApi.MfSpatialAudio.pas',
  WinApi.MediaFoundationApi.MfTransform in '..\src\WinApi.MediaFoundationApi.MfTransform.pas',
  WinApi.MediaFoundationApi.MfUtils in '..\src\WinApi.MediaFoundationApi.MfUtils.pas',
  WinApi.MediaFoundationApi.MfVirtualCamera in '..\src\WinApi.MediaFoundationApi.MfVirtualCamera.pas',
  WinApi.MediaFoundationApi.OpmApi in '..\src\WinApi.MediaFoundationApi.OpmApi.pas',
  WinApi.MediaFoundationApi.WmCodecDsp in '..\src\WinApi.MediaFoundationApi.WmCodecDsp.pas',
  WinApi.MediaFoundationApi.WmContainer in '..\src\WinApi.MediaFoundationApi.WmContainer.pas',
  WinApi.MediaObj in '..\src\WinApi.MediaObj.pas',
  WinApi.MfPack.PsApi in '..\src\WinApi.MfPack.PsApi.pas',
  WinApi.Mpeg2Bits in '..\src\WinApi.Mpeg2Bits.pas',
  WinApi.Mpeg2Data in '..\src\WinApi.Mpeg2Data.pas',
  WinApi.Mpeg2Error in '..\src\WinApi.Mpeg2Error.pas',
  WinApi.Mpeg2PsiParser in '..\src\WinApi.Mpeg2PsiParser.pas',
  WinApi.Mpeg2Structs in '..\src\WinApi.Mpeg2Structs.pas',
  WinApi.MpegType in '..\src\WinApi.MpegType.pas',
  WinApi.Relogger in '..\src\WinApi.Relogger.pas',
  WinApi.ServProv in '..\src\WinApi.ServProv.pas',
  WinApi.ShTypes in '..\src\WinApi.ShTypes.pas',
  WinApi.StrmIf in '..\src\WinApi.StrmIf.pas',
  WinApi.StructuredQueryCondition_2020 in '..\src\WinApi.StructuredQueryCondition_2020.pas',
  WinApi.Unknwn in '..\src\WinApi.Unknwn.pas',
  WinApi.UuIds in '..\src\WinApi.UuIds.pas',
  WinApi.VpType in '..\src\WinApi.VpType.pas',
  WinApi.WIC.WinCodec in '..\src\WinApi.WIC.WinCodec.pas',
  WinApi.WIC.WinCodecSdk in '..\src\WinApi.WIC.WinCodecSdk.pas',
  WinApi.WinApiTypes in '..\src\WinApi.WinApiTypes.pas',
  WinApi.WinError in '..\src\WinApi.WinError.pas',
  WinApi.WinMM.DigitalV in '..\src\WinApi.WinMM.DigitalV.pas',
  WinApi.WinMM.JoyStickApi in '..\src\WinApi.WinMM.JoyStickApi.pas',
  WinApi.WinMM.MCIApi in '..\src\WinApi.WinMM.MCIApi.pas',
  WinApi.WinMM.MCIAvi in '..\src\WinApi.WinMM.MCIAvi.pas',
  WinApi.WinMM.MMDdk in '..\src\WinApi.WinMM.MMDdk.pas',
  WinApi.WinMM.MMeApi in '..\src\WinApi.WinMM.MMeApi.pas',
  WinApi.WinMM.MMiscApi in '..\src\WinApi.WinMM.MMiscApi.pas',
  WinApi.WinMM.MMiscApi2 in '..\src\WinApi.WinMM.MMiscApi2.pas',
  WinApi.WinMM.MMReg in '..\src\WinApi.WinMM.MMReg.pas',
  WinApi.WinMM.MmStream in '..\src\WinApi.WinMM.MmStream.pas',
  WinApi.WinMM.MMSysCom in '..\src\WinApi.WinMM.MMSysCom.pas',
  WinApi.WinMM.MMSystem in '..\src\WinApi.WinMM.MMSystem.pas',
  WinApi.WinMM.MsAcm in '..\src\WinApi.WinMM.MsAcm.pas',
  WinApi.WinMM.MSAcmDlg in '..\src\WinApi.WinMM.MSAcmDlg.pas',
  WinApi.WinMM.MSAcmDrv in '..\src\WinApi.WinMM.MSAcmDrv.pas',
  WinApi.WinMM.PlaySoundApi in '..\src\WinApi.WinMM.PlaySoundApi.pas',
  WinApi.WinMM.TimeApi in '..\src\WinApi.WinMM.TimeApi.pas',
  WinApi.WinMM.VfW in '..\src\WinApi.WinMM.VfW.pas',
  WinApi.WinMM.VfwExt in '..\src\WinApi.WinMM.VfwExt.pas',
  WinApi.WinMM.VfwMsgs in '..\src\WinApi.WinMM.VfwMsgs.pas',
  WinApi.Wmistr in '..\src\WinApi.Wmistr.pas',
  WinApi.StiErr in '..\src\WinApi.StiErr.pas',
  WinApi.MfPack.VideoStandardsCheat in '..\src\WinApi.MfPack.VideoStandardsCheat.pas',
  WinApi.MediaFoundationApi.MfTrace in '..\src\WinApi.MediaFoundationApi.MfTrace.pas';

end.
