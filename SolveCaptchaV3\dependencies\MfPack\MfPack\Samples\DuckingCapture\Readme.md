# DuckingCapture Sample
Version: X 3.1.7

Description:
  ========================================================================
      WIN32 APPLICATION : Ducking Capture Sample Project Overview
  ========================================================================

  This sample implements a simple "Chat" that demonstrates to the "ducking" 
  feature in Windows 7. It simply captures samples from the sound card and 
  discards them.

  Note that this sample requires Windows 7 or later. 

NOTES: 
 - There are 2 versions of the sample. One that uses the function callback and 
   one that uses the window-callback (as used in the original CPP sample).
 - This release is updated for compiler version 17 up to 35.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 7 or later.
 - Minimum supported MfPack version: 3.1.2

Project: Media Foundation - MFPack - Samples
Project location: http://sourceforge.net/projects/MFPack

First release date: 05-07-2020
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.