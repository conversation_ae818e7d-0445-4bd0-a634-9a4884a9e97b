﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{87970E41-DB70-4744-9AF8-C0C4F7C3AA77}</ProjectGuid>
        <MainSource>WebView4Delphi.dpk</MainSource>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <ProjectName Condition="'$(ProjectName)'==''">WebView4Delphi</ProjectName>
        <TargetedPlatforms>1048577</TargetedPlatforms>
        <AppType>Package</AppType>
        <FrameworkType>VCL</FrameworkType>
        <ProjectVersion>20.1</ProjectVersion>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <DCC_E>false</DCC_E>
        <DCC_F>false</DCC_F>
        <DCC_K>false</DCC_K>
        <DCC_N>true</DCC_N>
        <DCC_S>false</DCC_S>
        <DCC_ImageBase>00400000</DCC_ImageBase>
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_Description>WebView4Delphi</DCC_Description>
        <DCC_UsePackage>vclx;vcl;rtl;dsnapcon;dsnap;dbrtl;vcldb;dbexpress;dbxcds;adortl;bdertl;vcldbx;ibxpress;teeui;teedb;tee;dss;visualclx;visualdbclx;vclactnband;$(DCC_UsePackage)</DCC_UsePackage>
        <DCC_Define>DEBUG;DEBUG;$(DCC_Define)</DCC_Define>
        <GenDll>true</GenDll>
        <GenPackage>true</GenPackage>
        <DCC_OutputNeverBuildDcps>true</DCC_OutputNeverBuildDcps>
        <SanitizedProjectName>WebView4Delphi</SanitizedProjectName>
        <DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;DUnitX.Loggers.GUI;Winapi;System.Win;$(DCC_Namespace)</DCC_Namespace>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=</VerInfo_Keys>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_RangeChecking>true</DCC_RangeChecking>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="rtl.dcp"/>
        <DCCReference Include="vcl.dcp"/>
        <DCCReference Include="WebView4DelphiVCL_register.pas"/>
        <DCCReference Include="..\source\uWVConstants.pas"/>
        <DCCReference Include="..\source\uWVLibFunctions.pas"/>
        <DCCReference Include="..\source\uWVLoader.pas"/>
        <DCCReference Include="..\source\uWVTypeLibrary.pas"/>
        <DCCReference Include="..\source\uWVTypes.pas"/>
        <DCCReference Include="..\source\uWVInterfaces.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Controller.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Settings.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Environment.pas"/>
        <DCCReference Include="..\source\uWVWinControl.pas"/>
        <DCCReference Include="..\source\uWVWindowParent.pas"/>
        <DCCReference Include="..\source\uWVBrowser.pas"/>
        <DCCReference Include="..\source\uWVMiscFunctions.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2HttpRequestHeaders.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2HttpHeadersCollectionIterator.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2EnvironmentOptions.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Delegates.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Args.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2WebResourceRequest.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2WebResourceResponse.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Deferral.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2PointerInfo.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2CompositionController.pas"/>
        <DCCReference Include="..\source\uWVEvents.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2PrintSettings.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2WebResourceResponseView.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2FrameInfoCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2FrameInfoCollectionIterator.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2FrameInfo.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2CookieManager.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Cookie.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2CookieList.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ClientCertificateCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ClientCertificate.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2StringCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2WindowFeatures.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2DownloadOperation.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Frame.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2HttpResponseHeaders.pas"/>
        <DCCReference Include="..\source\uWVBrowserBase.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ProcessInfoCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ProcessInfo.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2BasicAuthenticationResponse.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ContextMenuItemCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ContextMenuItem.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ContextMenuTarget.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Profile.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ControllerOptions.pas"/>
        <DCCReference Include="..\source\uWVLoaderInternal.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Certificate.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2CustomSchemeRegistration.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2PermissionSettingCollectionView.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2PermissionSetting.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2SharedBuffer.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ObjectCollectionView.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2File.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ProcessExtendedInfo.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ProcessExtendedInfoCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2BrowserExtension.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2BrowserExtensionList.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ScriptException.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ExecuteScriptResult.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2RegionRectCollectionView.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2ObjectCollection.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2FileSystemHandle.pas"/>
        <DCCReference Include="..\source\uWVCoreWebView2Notification.pas"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Package</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">WebView4Delphi.dpk</Source>
                </Source>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="iOSDevice32">False</Platform>
                <Platform value="iOSSimulator">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
                <Platform value="Win64x">True</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>
