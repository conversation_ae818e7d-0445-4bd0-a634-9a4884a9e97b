@echo off
echo ========================================
echo  TESTE DE COMPILACAO - SolveCaptchaV3
echo ========================================
echo.

cd WindowlessBrowser

echo Tentando compilar o projeto...
echo.

set DELPHI_PATH="c:\program files (x86)\embarcadero\studio\23.0\bin"
set PROJECT_PATH=%CD%

echo Delphi Path: %DELPHI_PATH%
echo Project Path: %PROJECT_PATH%
echo.

echo Executando compilacao...
%DELPHI_PATH%\dcc32.exe -B -Q WindowlessBrowser.dpr

if errorlevel 1 (
    echo.
    echo ❌ ERRO: Falha na compilacao
    echo Verifique os erros acima
    echo.
) else (
    echo.
    echo ✅ SUCESSO: Projeto compilado com sucesso!
    echo Executavel gerado em: %PROJECT_PATH%\WindowlessBrowser.exe
    echo.
)

echo ========================================
echo  TESTE FINALIZADO
echo ========================================
pause
