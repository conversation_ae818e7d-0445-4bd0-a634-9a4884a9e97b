@echo off
echo ========================================
echo  CORRIGINDO ERROS DE EXECUCAO
echo  SolveCaptchaV3 - WindowlessBrowser
echo ========================================
echo.

echo 1. Copiando WebView2Loader.dll...
copy "dependencies\WebView4Delphi\bin32\WebView2Loader.dll" "WindowlessBrowser\" >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Falha ao copiar WebView2Loader.dll
) else (
    echo ✅ WebView2Loader.dll copiado com sucesso
)

echo.
echo 2. Fazendo backup do arquivo .dfm original...
cd WindowlessBrowser
copy uWindowlessBrowser.dfm uWindowlessBrowser_backup.dfm >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Falha ao fazer backup do .dfm
) else (
    echo ✅ Backup do .dfm criado
)

echo.
echo 3. Aplicando arquivo .dfm corrigido...
copy uWindowlessBrowser_clean.dfm uWindowlessBrowser.dfm >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Falha ao aplicar .dfm corrigido
) else (
    echo ✅ Arquivo .dfm corrigido aplicado
)

echo.
echo 4. Verificando arquivos necessários...
if exist "WebView2Loader.dll" (
    echo ✅ WebView2Loader.dll presente
) else (
    echo ❌ WebView2Loader.dll ausente
)

if exist "uWindowlessBrowser.dfm" (
    echo ✅ uWindowlessBrowser.dfm presente
) else (
    echo ❌ uWindowlessBrowser.dfm ausente
)

if exist "config.ini" (
    echo ✅ config.ini presente
) else (
    echo ⚠️  config.ini não encontrado - será criado automaticamente
)

cd ..

echo.
echo ========================================
echo  CORRECOES APLICADAS!
echo ========================================
echo.
echo PROXIMOS PASSOS:
echo 1. Abra o Delphi IDE
echo 2. Abra o projeto WindowlessBrowser.dproj
echo 3. Pressione F9 para compilar e executar
echo 4. Configure sua chave OpenAI no config.ini
echo.
echo Se ainda houver erros, verifique:
echo - Se o WebView2 Runtime está instalado
echo - Se todas as dependências estão corretas
echo - Se a chave OpenAI está configurada
echo.
pause
