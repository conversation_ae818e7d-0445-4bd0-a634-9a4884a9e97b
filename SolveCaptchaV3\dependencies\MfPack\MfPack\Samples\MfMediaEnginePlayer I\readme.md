# mfMediaPlayer I
Version: X 3.1.7

NOTES: 
 - This release is updated for compiler version 17 up to 34.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 7 or later.
 - Minimum supported MfPack version: 3.1.5

This is the basic class of MfMediaEnginePlayer,
containing the necessary methodes to play a mediafile.
 
For indepth information see the Microsoft MediaEnginePlayer example and
documentation, containing the complete information about MfMediaEnginePlayer.

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 05-02-2016
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.