# MfComponents
Version: X 3.1.7

NOTES: 
 - This release is updated for compiler version 17 up to 35.
 - SDK version: 10.0.26100.0 (Win 11)
 - Requires Windows 7 or later.
 - Minimum supported MfPack version: 3.1.6

MfPack Visual Components and classes
====================================

This package contains the following -installable- components:

- TQTimer - Queue Timer component. For precise timing.
- MfPeakMeter - Simple PeakMeter control.
- MfPeakMeterEx - Enhanged PeakMeter control.
- MfAudioEndPoint - Audio EndPoint component that responds to volume changes. 
                    Uses: IAudioEndpointVolumeEx, IAudioEndpointVolumeCallback and IMMDeviceEnumerator.
- TThreadedQTimer - A threaded queue timer (experimental) 

Project: Media Foundation - MFPack - Samples
Project location: https://github.com/FactoryXCode/MfPack
                  https://sourceforge.net/projects/MFPack

First release date: 05-02-2016
Final release date: 30-05-2024

Copyright © FactoryX. All rights reserved.